import requests
import time
import re
import random

"""
python -m nuitka \
--standalone \
--show-memory \
--show-progress \
--nofollow-imports \
--output-dir=o nuitka_demo.py
"""
def get_response():
    img = 'https://i-blog.csdnimg.cn/blog_migrate/79b514f3469aa57a2ec39ca528f98a7f.png'
    response = requests.get(img).content
    print(f'size of response: {len(response)} bytes')
    print(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))

    # 随机延时
    delay = random.randint(1, 3)
    print(f'random delay: {delay} seconds')
    time.sleep(delay)

    s = '你好，我是机器人小智，很高兴为您服务。'
    print(re.search(r'小智', s))
    return response


if __name__ == '__main__':
    get_response()
