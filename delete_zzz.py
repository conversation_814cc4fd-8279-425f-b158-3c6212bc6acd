import os
import zipfile


def filter_zip_files(folder_path):
    """Traverse the folder and process each zip file found."""
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.zip'):
                zip_path = os.path.join(root, file)
                process_zip(zip_path)


def process_zip(zip_path):
    """Open a zip file, filter out files starting with 'zzz', and resave it."""
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        # Get the list of files in the zip
        file_list = zip_ref.namelist()

        # Filter out files starting with 'zzz'
        filtered_files = [file for file in file_list if 'z' not in os.path.basename(file) and 'Z' not in os.path.basename(file)]

        # Proceed only if some files were filtered out
        if len(filtered_files) < len(file_list) and len(filtered_files) != 0:
            print(f"{zip_path} has zzz files")
            # Create a temporary new zip file
            new_zip_path = zip_path + '.new'
            with zipfile.ZipFile(new_zip_path, 'w', zipfile.ZIP_DEFLATED) as new_zip:
                for file in filtered_files:
                    # Extract the file content and write it to the new zip
                    file_content = zip_ref.read(file)
                    new_zip.writestr(file, file_content)

            # Replace the original zip with the new filtered one
            os.remove(zip_path)
            os.rename(new_zip_path, zip_path)


# Get folder path from user and start processing
# folder_path = input("请输入文件夹路径：")
folder_path = "/home/<USER>/Downloads/[たくろう]"
filter_zip_files(folder_path)