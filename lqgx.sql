/*
 Navicat Premium Dump SQL

 Source Server         : localhost
 Source Server Type    : PostgreSQL
 Source Server Version : 170003 (170003)
 Source Host           : localhost:5432
 Source Catalog        : lqgx
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170003 (170003)
 File Encoding         : 65001

 Date: 23/05/2025 11:24:45
*/


-- ----------------------------
-- Type structure for account_owner_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."account_owner_type_enum";
CREATE TYPE "public"."account_owner_type_enum" AS ENUM (
  'USER',
  'PLATFORM'
);
ALTER TYPE "public"."account_owner_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for action_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."action_type_enum";
CREATE TYPE "public"."action_type_enum" AS ENUM (
  'VIEW',
  'USE',
  'DOWNLOAD',
  'SHARE',
  'COMPLETE',
  'CHAT'
);
ALTER TYPE "public"."action_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for activity_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."activity_status_enum";
CREATE TYPE "public"."activity_status_enum" AS ENUM (
  'DRAFT',
  'ACTIVE',
  'ENDED',
  'CANCELLED'
);
ALTER TYPE "public"."activity_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for agent_order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."agent_order_status_enum";
CREATE TYPE "public"."agent_order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."agent_order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for agent_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."agent_type_enum";
CREATE TYPE "public"."agent_type_enum" AS ENUM (
  'CHAT',
  'TEXT',
  'WORKFLOW'
);
ALTER TYPE "public"."agent_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for consult_order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."consult_order_status_enum";
CREATE TYPE "public"."consult_order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."consult_order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for coupon_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."coupon_status_enum";
CREATE TYPE "public"."coupon_status_enum" AS ENUM (
  'UNUSED',
  'USED',
  'EXPIRED'
);
ALTER TYPE "public"."coupon_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for coupon_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."coupon_type_enum";
CREATE TYPE "public"."coupon_type_enum" AS ENUM (
  'FIXED',
  'PERCENT'
);
ALTER TYPE "public"."coupon_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for course_order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."course_order_status_enum";
CREATE TYPE "public"."course_order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."course_order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for course_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."course_type_enum";
CREATE TYPE "public"."course_type_enum" AS ENUM (
  'SY',
  'CH',
  'YX',
  'TRZ',
  'GL',
  'YY',
  'WXXL',
  'LQGX',
  'QLSD',
  'JWH',
  'LQKY',
  'LTJX',
  'LYWY',
  'QLYJ'
);
ALTER TYPE "public"."course_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for gender_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."gender_enum";
CREATE TYPE "public"."gender_enum" AS ENUM (
  'MALE',
  'FEMALE',
  'UNKNOWN'
);
ALTER TYPE "public"."gender_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for member_order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."member_order_status_enum";
CREATE TYPE "public"."member_order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."member_order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."order_status_enum";
CREATE TYPE "public"."order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for order_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."order_type_enum";
CREATE TYPE "public"."order_type_enum" AS ENUM (
  'COURSE',
  'AGENT',
  'CONSULT',
  'MEMBER'
);
ALTER TYPE "public"."order_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for resource_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."resource_type_enum";
CREATE TYPE "public"."resource_type_enum" AS ENUM (
  'AGENT',
  'COURSE',
  'COURSE_SECTION'
);
ALTER TYPE "public"."resource_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for user_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."user_type_enum";
CREATE TYPE "public"."user_type_enum" AS ENUM (
  'WECHAT',
  'LOCAL'
);
ALTER TYPE "public"."user_type_enum" OWNER TO "admin";

-- ----------------------------
-- Sequence structure for asm_activities_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_activities_id_seq";
CREATE SEQUENCE "public"."asm_activities_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_activity_reward_records_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_activity_reward_records_id_seq";
CREATE SEQUENCE "public"."asm_activity_reward_records_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_agent_starters_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_agent_starters_id_seq";
CREATE SEQUENCE "public"."asm_agent_starters_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_agents_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_agents_id_seq";
CREATE SEQUENCE "public"."asm_agents_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_coupons_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_coupons_id_seq";
CREATE SEQUENCE "public"."asm_coupons_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_course_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_course_id_seq";
CREATE SEQUENCE "public"."asm_course_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_course_section_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_course_section_id_seq";
CREATE SEQUENCE "public"."asm_course_section_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_investment_show_application_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_investment_show_application_id_seq";
CREATE SEQUENCE "public"."asm_investment_show_application_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_investment_show_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_investment_show_id_seq";
CREATE SEQUENCE "public"."asm_investment_show_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_orders_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_orders_id_seq";
CREATE SEQUENCE "public"."asm_orders_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_system_parameters_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_system_parameters_id_seq";
CREATE SEQUENCE "public"."asm_system_parameters_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_teacher_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_teacher_id_seq";
CREATE SEQUENCE "public"."asm_teacher_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_user_accounts_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_user_accounts_id_seq";
CREATE SEQUENCE "public"."asm_user_accounts_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_user_usages_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_user_usages_id_seq";
CREATE SEQUENCE "public"."asm_user_usages_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_users_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_users_id_seq";
CREATE SEQUENCE "public"."asm_users_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for qu_user_assets_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."qu_user_assets_id_seq";
CREATE SEQUENCE "public"."qu_user_assets_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Table structure for alembic_version
-- ----------------------------
DROP TABLE IF EXISTS "public"."alembic_version";
CREATE TABLE "public"."alembic_version" (
  "version_num" varchar(32) COLLATE "pg_catalog"."default" NOT NULL
)
;

-- ----------------------------
-- Records of alembic_version
-- ----------------------------
INSERT INTO "public"."alembic_version" VALUES ('b03635dcf4a0');

-- ----------------------------
-- Table structure for asm_activities
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_activities";
CREATE TABLE "public"."asm_activities" (
  "id" int4 NOT NULL DEFAULT nextval('asm_activities_id_seq'::regclass),
  "title" varchar COLLATE "pg_catalog"."default",
  "start_time" timestamp(6),
  "end_time" timestamp(6),
  "status" "public"."activity_status_enum",
  "conditions" varchar COLLATE "pg_catalog"."default",
  "rewards" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_activities"."id" IS '活动ID';
COMMENT ON COLUMN "public"."asm_activities"."title" IS '活动标题';
COMMENT ON COLUMN "public"."asm_activities"."start_time" IS '活动开始时间';
COMMENT ON COLUMN "public"."asm_activities"."end_time" IS '活动结束时间';
COMMENT ON COLUMN "public"."asm_activities"."status" IS '活动状态';
COMMENT ON COLUMN "public"."asm_activities"."conditions" IS '达成条件(JSON格式)';
COMMENT ON COLUMN "public"."asm_activities"."rewards" IS '奖励内容(JSON格式)';
COMMENT ON COLUMN "public"."asm_activities"."created_at" IS '创建时间';
COMMENT ON TABLE "public"."asm_activities" IS '活动表';

-- ----------------------------
-- Records of asm_activities
-- ----------------------------
INSERT INTO "public"."asm_activities" VALUES (2, '聊天送优惠券', '2025-01-18 16:00:00', '2044-01-06 16:00:00', 'DRAFT', '{"type":"CHAT_COUNT","params":{"required_count":3}}', '{"type":"coupon","coupons":[{"name":"限时特惠券","code":"SPECIAL_100","type":"FIXED","value":1,"minAmount":500,"status":"UNUSED"}]}', '2025-01-18 20:54:20.823');
INSERT INTO "public"."asm_activities" VALUES (3, '新人优惠券', '2025-01-20 16:00:00', '2029-12-22 16:00:00', 'ACTIVE', '{"type":"NEWUSER_COUPON"}', '{"type":"coupon","coupons":[{"name":"新人优惠券","code":"NEWUSER_100","type":"FIXED","value":100,"minAmount":0,"status":"UNUSED"}]}', '2025-01-20 23:33:59.248');

-- ----------------------------
-- Table structure for asm_activity_reward_records
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_activity_reward_records";
CREATE TABLE "public"."asm_activity_reward_records" (
  "id" int4 NOT NULL DEFAULT nextval('asm_activity_reward_records_id_seq'::regclass),
  "user_id" int4,
  "reward_type" varchar COLLATE "pg_catalog"."default",
  "reward_content" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_activity_reward_records"."id" IS '记录ID';
COMMENT ON COLUMN "public"."asm_activity_reward_records"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_activity_reward_records"."reward_type" IS '奖励类型';
COMMENT ON COLUMN "public"."asm_activity_reward_records"."reward_content" IS '领取的奖励内容(JSON格式)';
COMMENT ON COLUMN "public"."asm_activity_reward_records"."created_at" IS '领取时间';
COMMENT ON TABLE "public"."asm_activity_reward_records" IS '活动奖励领取记录表';

-- ----------------------------
-- Records of asm_activity_reward_records
-- ----------------------------
INSERT INTO "public"."asm_activity_reward_records" VALUES (2, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 10:18:43.942914');
INSERT INTO "public"."asm_activity_reward_records" VALUES (3, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 16:14:08.991505');
INSERT INTO "public"."asm_activity_reward_records" VALUES (4, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 19:01:35.45559');
INSERT INTO "public"."asm_activity_reward_records" VALUES (5, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 19:03:06.141307');
INSERT INTO "public"."asm_activity_reward_records" VALUES (6, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 19:12:33.553939');
INSERT INTO "public"."asm_activity_reward_records" VALUES (7, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:24:40.904141');
INSERT INTO "public"."asm_activity_reward_records" VALUES (8, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:28:03.975716');
INSERT INTO "public"."asm_activity_reward_records" VALUES (9, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:28:28.127166');
INSERT INTO "public"."asm_activity_reward_records" VALUES (10, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:28:56.699599');
INSERT INTO "public"."asm_activity_reward_records" VALUES (11, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:29:54.393432');
INSERT INTO "public"."asm_activity_reward_records" VALUES (12, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:31:28.08881');
INSERT INTO "public"."asm_activity_reward_records" VALUES (13, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:48:09.501789');
INSERT INTO "public"."asm_activity_reward_records" VALUES (14, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:51:04.10741');
INSERT INTO "public"."asm_activity_reward_records" VALUES (15, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:51:45.666019');
INSERT INTO "public"."asm_activity_reward_records" VALUES (16, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 21:01:36.824595');
INSERT INTO "public"."asm_activity_reward_records" VALUES (17, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 06:31:19.002754');
INSERT INTO "public"."asm_activity_reward_records" VALUES (18, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 07:16:15.773368');
INSERT INTO "public"."asm_activity_reward_records" VALUES (19, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 07:20:28.59565');
INSERT INTO "public"."asm_activity_reward_records" VALUES (20, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 19:47:30.449132');
INSERT INTO "public"."asm_activity_reward_records" VALUES (21, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 23:46:55.668831');
INSERT INTO "public"."asm_activity_reward_records" VALUES (22, 9, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 11:15:34.963148');
INSERT INTO "public"."asm_activity_reward_records" VALUES (23, 9, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 11:15:36.181891');
INSERT INTO "public"."asm_activity_reward_records" VALUES (24, 9, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 11:15:36.28202');
INSERT INTO "public"."asm_activity_reward_records" VALUES (25, 8, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 11:21:35.234343');
INSERT INTO "public"."asm_activity_reward_records" VALUES (26, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 15:13:24.300311');
INSERT INTO "public"."asm_activity_reward_records" VALUES (27, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 15:16:23.988667');
INSERT INTO "public"."asm_activity_reward_records" VALUES (28, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 15:16:53.687323');
INSERT INTO "public"."asm_activity_reward_records" VALUES (29, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 18:06:56.732037');
INSERT INTO "public"."asm_activity_reward_records" VALUES (30, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 18:12:26.640841');
INSERT INTO "public"."asm_activity_reward_records" VALUES (31, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 18:24:04.3662');
INSERT INTO "public"."asm_activity_reward_records" VALUES (32, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 18:45:58.712057');
INSERT INTO "public"."asm_activity_reward_records" VALUES (33, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 19:55:18.581655');
INSERT INTO "public"."asm_activity_reward_records" VALUES (34, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 20:01:51.282289');
INSERT INTO "public"."asm_activity_reward_records" VALUES (35, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 20:03:53.21793');
INSERT INTO "public"."asm_activity_reward_records" VALUES (36, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 20:19:44.646593');
INSERT INTO "public"."asm_activity_reward_records" VALUES (37, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-25 10:05:46.666452');
INSERT INTO "public"."asm_activity_reward_records" VALUES (38, 9, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-25 10:05:50.632969');
INSERT INTO "public"."asm_activity_reward_records" VALUES (39, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-25 15:11:55.078446');
INSERT INTO "public"."asm_activity_reward_records" VALUES (40, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-26 14:37:37.537703');
INSERT INTO "public"."asm_activity_reward_records" VALUES (41, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-26 14:43:13.366428');
INSERT INTO "public"."asm_activity_reward_records" VALUES (42, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-26 14:48:41.651645');
INSERT INTO "public"."asm_activity_reward_records" VALUES (43, 18, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-13 21:28:33.454503');
INSERT INTO "public"."asm_activity_reward_records" VALUES (44, 8, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-15 12:25:37.508723');
INSERT INTO "public"."asm_activity_reward_records" VALUES (45, 19, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-17 10:09:10.150937');
INSERT INTO "public"."asm_activity_reward_records" VALUES (46, 25, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-18 10:15:19.857614');
INSERT INTO "public"."asm_activity_reward_records" VALUES (47, 25, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-18 10:24:01.049434');
INSERT INTO "public"."asm_activity_reward_records" VALUES (48, 22, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-18 10:25:55.446272');
INSERT INTO "public"."asm_activity_reward_records" VALUES (49, 28, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-20 14:52:23.11455');
INSERT INTO "public"."asm_activity_reward_records" VALUES (50, 28, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-20 15:06:00.894');
INSERT INTO "public"."asm_activity_reward_records" VALUES (51, 35, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-20 15:18:48.094575');
INSERT INTO "public"."asm_activity_reward_records" VALUES (52, 37, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-20 16:03:55.915814');
INSERT INTO "public"."asm_activity_reward_records" VALUES (53, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-25 11:18:16.356133');
INSERT INTO "public"."asm_activity_reward_records" VALUES (54, 49, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-26 11:34:32.712419');
INSERT INTO "public"."asm_activity_reward_records" VALUES (55, 55, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-05 12:40:18.751008');
INSERT INTO "public"."asm_activity_reward_records" VALUES (56, 58, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-06 15:18:11.650924');
INSERT INTO "public"."asm_activity_reward_records" VALUES (57, 57, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-07 15:37:21.016388');
INSERT INTO "public"."asm_activity_reward_records" VALUES (58, 55, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-11 17:33:31.780178');
INSERT INTO "public"."asm_activity_reward_records" VALUES (59, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-12 15:59:36.701671');
INSERT INTO "public"."asm_activity_reward_records" VALUES (60, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-12 15:59:50.017481');
INSERT INTO "public"."asm_activity_reward_records" VALUES (61, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-12 16:19:09.574537');
INSERT INTO "public"."asm_activity_reward_records" VALUES (62, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-12 16:19:46.259454');
INSERT INTO "public"."asm_activity_reward_records" VALUES (63, 81, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-12 19:05:23.377471');
INSERT INTO "public"."asm_activity_reward_records" VALUES (64, 81, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-12 19:48:35.728805');
INSERT INTO "public"."asm_activity_reward_records" VALUES (65, 83, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-13 11:57:32.757108');
INSERT INTO "public"."asm_activity_reward_records" VALUES (66, 84, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-13 12:35:33.615312');
INSERT INTO "public"."asm_activity_reward_records" VALUES (67, 88, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-13 16:56:28.615558');
INSERT INTO "public"."asm_activity_reward_records" VALUES (68, 100, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-14 08:48:38.37022');
INSERT INTO "public"."asm_activity_reward_records" VALUES (69, 101, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-14 09:12:53.320589');
INSERT INTO "public"."asm_activity_reward_records" VALUES (70, 55, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-14 12:53:46.903077');
INSERT INTO "public"."asm_activity_reward_records" VALUES (71, 108, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-14 19:54:18.7812');
INSERT INTO "public"."asm_activity_reward_records" VALUES (72, 114, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-14 19:56:11.321695');
INSERT INTO "public"."asm_activity_reward_records" VALUES (73, 164, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-15 19:48:41.46973');
INSERT INTO "public"."asm_activity_reward_records" VALUES (74, 167, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-16 10:21:26.979933');
INSERT INTO "public"."asm_activity_reward_records" VALUES (75, 167, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-16 10:26:47.456568');
INSERT INTO "public"."asm_activity_reward_records" VALUES (76, 168, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-16 14:58:00.626244');
INSERT INTO "public"."asm_activity_reward_records" VALUES (77, 177, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-17 20:14:04.542418');
INSERT INTO "public"."asm_activity_reward_records" VALUES (78, 180, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-18 11:15:14.995675');
INSERT INTO "public"."asm_activity_reward_records" VALUES (79, 184, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-18 12:12:48.41149');
INSERT INTO "public"."asm_activity_reward_records" VALUES (80, 186, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-18 12:39:59.425242');
INSERT INTO "public"."asm_activity_reward_records" VALUES (81, 187, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-18 12:59:03.788138');
INSERT INTO "public"."asm_activity_reward_records" VALUES (82, 189, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-18 15:20:10.436689');
INSERT INTO "public"."asm_activity_reward_records" VALUES (83, 191, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-18 19:55:13.573074');
INSERT INTO "public"."asm_activity_reward_records" VALUES (84, 188, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-19 17:58:06.482063');
INSERT INTO "public"."asm_activity_reward_records" VALUES (85, 177, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-19 20:11:27.296348');
INSERT INTO "public"."asm_activity_reward_records" VALUES (86, 177, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-20 09:53:34.078299');

-- ----------------------------
-- Table structure for asm_admins
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_admins";
CREATE TABLE "public"."asm_admins" (
  "id" int4 NOT NULL DEFAULT nextval('asm_users_id_seq'::regclass),
  "username" varchar(50) COLLATE "pg_catalog"."default",
  "password" varchar(100) COLLATE "pg_catalog"."default",
  "avatar_url" text COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_admins"."id" IS '管理员ID';
COMMENT ON COLUMN "public"."asm_admins"."username" IS '用户名';
COMMENT ON COLUMN "public"."asm_admins"."password" IS '密码';
COMMENT ON COLUMN "public"."asm_admins"."avatar_url" IS '头像URL';
COMMENT ON COLUMN "public"."asm_admins"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_admins"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_admins" IS '管理员表';

-- ----------------------------
-- Records of asm_admins
-- ----------------------------
INSERT INTO "public"."asm_admins" VALUES (5, 'dongjak', '$2a$12$EjonnAhGY4WActrvn77r..AqBgouGYuPWfJDmplV7cvf8oPZ2gyDK', NULL, '2025-01-16 22:12:34', '2025-01-16 22:12:36');

-- ----------------------------
-- Table structure for asm_agent_starters
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_agent_starters";
CREATE TABLE "public"."asm_agent_starters" (
  "id" int4 NOT NULL DEFAULT nextval('asm_agent_starters_id_seq'::regclass),
  "question" varchar COLLATE "pg_catalog"."default",
  "agent_id" int4,
  "dify_key" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_agent_starters"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_agent_starters"."question" IS '引导问题';
COMMENT ON COLUMN "public"."asm_agent_starters"."agent_id" IS '关联智能体ID';
COMMENT ON COLUMN "public"."asm_agent_starters"."dify_key" IS 'Dify密钥';
COMMENT ON COLUMN "public"."asm_agent_starters"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_agent_starters"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_agent_starters" IS 'AI智能体引导词表';

-- ----------------------------
-- Records of asm_agent_starters
-- ----------------------------

-- ----------------------------
-- Table structure for asm_agents
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_agents";
CREATE TABLE "public"."asm_agents" (
  "id" int4 NOT NULL DEFAULT nextval('asm_agents_id_seq'::regclass),
  "name" varchar COLLATE "pg_catalog"."default",
  "icon" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "type" "public"."agent_type_enum",
  "dify_key" varchar COLLATE "pg_catalog"."default",
  "parameters" json,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "tags" jsonb,
  "daily_free_count" int4,
  "price" float8
)
;
COMMENT ON COLUMN "public"."asm_agents"."id" IS '智能体ID';
COMMENT ON COLUMN "public"."asm_agents"."name" IS '智能体名称';
COMMENT ON COLUMN "public"."asm_agents"."icon" IS '图标URL';
COMMENT ON COLUMN "public"."asm_agents"."description" IS '功能描述';
COMMENT ON COLUMN "public"."asm_agents"."type" IS '智能体类型';
COMMENT ON COLUMN "public"."asm_agents"."dify_key" IS 'Dify密钥';
COMMENT ON COLUMN "public"."asm_agents"."parameters" IS '参数配置';
COMMENT ON COLUMN "public"."asm_agents"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_agents"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_agents"."tags" IS '标签列表，如：["人气导师", "从业年丰富"]';
COMMENT ON COLUMN "public"."asm_agents"."daily_free_count" IS '每日免费使用次数';
COMMENT ON COLUMN "public"."asm_agents"."price" IS '价格';
COMMENT ON TABLE "public"."asm_agents" IS 'AI智能体表';

-- ----------------------------
-- Records of asm_agents
-- ----------------------------
INSERT INTO "public"."asm_agents" VALUES (20, '爆款小说作家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319143471-%E5%B0%8F%E8%AF%B4.png', '爽文小说作家，写作的手法参考《鹿鼎记》《灵境行者》《全球高武》《万族之劫》《诡秘之主》《凡人修仙传》《圣墟》《这游戏也太真实了》。
主线悬念一定要足够强，必须贯穿全程。
核心的故事，一定包含了强有力的看点，能够全程让主角奔着目标奋力向前。', 'CHAT', 'app-UVwjSunMJhGzUFmDb47AqKAu', '{}', '2025-02-23 11:22:16.184', '2025-03-27 00:15:36.326', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (1, 'ai创业大师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737275962710-53114846bf9b4cbd9c362244e8003dc.jpg', '', 'CHAT', 'app-vvkpIqRJn7fHbeTjV61igvTM', '{}', '2025-01-18 14:14:40.839', '2025-03-19 12:22:32.54', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (42, '数字人', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319191235-%E8%A7%86%E9%A2%91.png', '数字人', 'CHAT', 'app-MjrsJ3yDn704Gp01Z2A5hfEw', '{}', '2025-02-23 11:30:15.365', '2025-02-24 02:26:44.777', '[]', 0, 0.01);
INSERT INTO "public"."asm_agents" VALUES (12, '短视频脚本创作', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737409882296-9d90fe0e-90aa-446c-8eac-329f2a87f45b.png', NULL, 'CHAT', 'app-ddhcS30uPRkwE2Ne9LOZPVc3', '{}', '2025-01-20 21:38:40.404', '2025-03-27 00:15:20.64', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (21, '金句提炼大师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319166072-iconfont-shuru.png', '从给定的信息中归纳出“金句”。', 'CHAT', 'app-JqJ6esmXaSkOassuXAicppfY', '{}', '2025-02-23 11:24:48.629', '2025-03-27 00:15:48.959', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (22, '抖音带货直播间话术大师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319191235-%E8%A7%86%E9%A2%91.png', '专业的抖音带货直播间话术撰写大师', 'CHAT', 'app-n6qNIOJesTXQc57hZMImXR4U', '{}', '2025-02-23 11:30:15.365', '2025-03-27 00:16:01.95', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (23, '口播文案助理', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319214371-%E6%96%87%E6%A1%88%E7%AD%96%E5%88%92.png', '口播小助理，如果您需要我帮忙改写文案，请直接甩视频链接给我！如果您需要我自主生成口播文案，请直接告诉我行业或者产品关键词。', 'CHAT', 'app-iXjQbIye7OCWwWe4Ks5cQIwI', '{}', '2025-02-23 11:47:06.114', '2025-03-27 00:16:17.39', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (24, '短视频内容创作专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319247179-%E7%BC%96%E8%BE%91%E6%96%87%E6%A1%88.png', '短视频内容创作专家，精通爆款口播视频的创作规律和传播机制.', 'CHAT', 'app-Jso3722BxA6qz6yzthBeAVIr', '{}', '2025-02-23 11:55:43.383', '2025-03-27 00:16:28.75', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (25, '短视频编导大师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319270993-%E8%A7%86%E9%A2%91%20(1).png', '你是一位顶级短视频编导，精通爆款内容创作的底层逻辑，你的目标是用最短时间触发人性情绪，创造高完播、高互动的短视频', 'CHAT', 'app-VIXDT8c7WmTsbVEG8ZksfJiC', '{}', '2025-02-23 11:59:17.676', '2025-03-27 00:17:50.708', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (26, '顶级家教老师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319288360-%E4%B8%8A%E9%97%A8%E5%AE%B6%E6%95%99.png', '资深的培训机构顶级老师，专门帮助学生提升学习成绩，并为家长提供科学的学习指导建议.', 'CHAT', 'app-uj9RUeWwqMB6PKRv6ffe197D', '{}', '2025-02-23 12:01:58.042', '2025-03-27 00:18:04.518', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (27, '直播话术专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319335426-%E7%BB%84%204235.png', '直播话术专家，专门优化主播的表达方式，帮助提升直播间互动率、留存率和转化率', 'CHAT', 'app-Z1pgFy6ONjvfZLO3I6Ej7W9i', '{}', '2025-02-23 12:03:23.969', '2025-03-27 00:18:30.288', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (28, '马斯克', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319318508-125_%E9%A9%AC%E6%96%AF%E5%85%8B.png', '伊隆·马斯克（Elon Musk），全球顶尖的企业家、工程师和未来主义者', 'CHAT', 'app-h6rmmPYMHskLjyGuRVDod37L', '{}', '2025-02-23 12:10:14.662', '2025-03-27 00:18:16.201', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (29, '短视频内容架构师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319358588-%E8%A7%86%E9%A2%91%20(3).png', '短视频内容架构师，专注于打造高留存、高互动、高传播的视频文案', 'CHAT', 'app-lQet0ugAiv0kJTlQiPfmpqBa', '{}', '2025-02-23 12:18:54.772', '2025-03-27 00:18:45.028', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (30, '选题策划专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319397122-%E9%80%89%E9%A2%98%E7%AD%96%E5%88%92.png', '资深的选题策划专家，擅长帮助内容创作者、媒体工作者、自媒体博主、品牌营销人员等制定有传播力的选题', 'CHAT', 'app-rFHZCFEeBdadCMlZJJQjXfeE', '{}', '2025-02-23 12:20:49.099', '2025-03-27 01:12:11.508', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (31, '情感疗愈师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319412794-%E7%B2%BE%E7%A5%9E%E5%BF%83%E7%90%86%E7%A7%91.png', '情感疗愈师，具有深厚的心理学和情感修复经验，专长包括情绪管理、亲密关系恢复、自尊建设、创伤疗愈及个人成长领域。你的目标是通过温暖且富有同理心的对话，帮助用户探索他们的内在情绪，提供鼓励、指导和方法，助力他们走出困境，逐步实现自我疗愈和成长。', 'CHAT', 'app-XoOtY6DAFsxqmCjd4ZzXXngs', '{}', '2025-02-23 12:30:29.927', '2025-03-27 01:13:50.736', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (33, 'IP 孵化专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319451171-%E6%96%B9%E5%90%91-IP.png', ' 创始人个人品牌（IP）孵化 的资深顾问，主要帮助创业者、企业家、投资人、行业专家、高管等打造强个人品牌', 'CHAT', 'app-lu8LIfSbeex9vpsGzEFh5LoM', '{}', '2025-02-23 12:41:27.249', '2025-03-27 01:15:02.7', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (32, '爆款文案专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319435790-icon_%E6%96%87%E6%A1%88%E7%94%9F%E6%88%90-%E4%B8%93%E4%B8%9A%E8%AF%84%E4%BB%B7%E6%96%87%E6%A1%88.png', '爆款文案专家，专门研究各平台（如视频号、小红书、抖音、朋友圈、知乎、微博、公众号等）上的爆款文案，并能精准模仿它们的风格。', 'CHAT', 'app-tHyYDRAtbFB0lUTvvu9VfeAM', '{}', '2025-02-23 12:38:55.958', '2025-03-27 01:14:20.28', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (34, '实体商家获客专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319500513-%E6%96%87%E6%A1%88%E7%AE%A1%E7%90%86.png', '专门帮助实体商家（如餐饮店、美容院、健身房、零售店等）获取更多精准的线下客户', 'CHAT', 'app-fvg4qldlcIqIE8ibrnPj27PE', '{}', '2025-02-23 12:46:25.588', '2025-03-27 01:15:44.732', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (35, '视频编导策划师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319593751-%E8%A7%86%E9%A2%91%E7%9B%B4%E6%92%AD.png', '视频编导策划师，专门设计适合不同平台（如抖音、快手、B站、小红书、视频号、YouTube 以及 Instagram Reels）的爆款视频脚本和创意内容', 'CHAT', 'app-pGGdaXXDa70mivuOJdkoIQT3', '{}', '2025-02-23 12:50:46.155', '2025-03-27 01:16:06.36', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (36, '文案润色专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319610631-%E6%96%87%E6%A1%88-copy-copy.png', '文本优化和编辑', 'CHAT', 'app-jkx3zJ5TUeM0Zpd4UkW5aTQP', '{}', '2025-02-23 13:07:11.848', '2025-03-27 01:17:40.658', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (37, '中国法律助手', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319563412-%E6%B3%95%E5%BE%8B%E6%8F%B4%E5%8A%A9.png', '专门用于解决法律咨询和协助起草符合中国法律标准的法律文件的工具', 'CHAT', 'app-DDc1CEruudx5u7xwGCfcZ1B2', '{}', '2025-02-23 13:20:03.286', '2025-03-27 01:16:23.312', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (38, '文案仿写', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319538923-%E6%96%87%E6%A1%88%E6%96%87%E6%A1%88-copy.png', '根据已知的文案和仿写要求进行文案的仿写', 'CHAT', 'app-exJRmvvRUaeE3SAs7Lq1bIjf', '{}', '2025-02-23 13:23:55.239', '2025-03-27 01:16:48.981', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (10, ' 抖音短视频脚本', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737409865448-asst_YQgkkuWiReKhKAq7FK46ToKe.png', NULL, 'CHAT', 'app-j1XbW5lGtXwIyrw8GwdQwBID', '{}', '2025-01-20 21:35:30.451', '2025-03-19 12:11:16.498', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (11, ' 小红书脚本', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737409466035-2e4b5de0-f29e-4903-82be-e39e12e1871e%20(1).png', NULL, 'CHAT', 'app-ibUZpLqVxQRFq4sn8lcWDJ42', '{}', '2025-01-20 21:37:14.265', '2025-03-27 00:15:08.778', '[]', 1, 0.01);
INSERT INTO "public"."asm_agents" VALUES (39, '青少年心理学专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740320305246-%E9%87%8D%E7%82%B9%E9%9D%92%E5%B0%91%E5%B9%B4.png', '青少年心理学专家，深谙青少年心理发展的规律和特点。你擅长解析青少年在成长过程中遇到的心理问题，比如学业压力、人际关系、情绪调节、自我认同、青春期矛盾等。', 'CHAT', 'app-pJv1WfcaMOd8d3U0NI5v9qXT', '{}', '2025-02-23 14:11:07.864', '2025-03-27 01:17:06.725', '[]', 1, 0.01);

-- ----------------------------
-- Table structure for asm_coupons
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_coupons";
CREATE TABLE "public"."asm_coupons" (
  "id" int4 NOT NULL DEFAULT nextval('asm_coupons_id_seq'::regclass),
  "name" varchar COLLATE "pg_catalog"."default",
  "type" "public"."coupon_type_enum",
  "value" float8,
  "min_amount" float8,
  "start_time" timestamp(6),
  "end_time" timestamp(6),
  "status" "public"."coupon_status_enum",
  "user_id" int4,
  "used_time" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_coupons"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_coupons"."name" IS '优惠券名称';
COMMENT ON COLUMN "public"."asm_coupons"."type" IS '优惠券类型';
COMMENT ON COLUMN "public"."asm_coupons"."value" IS '优惠券面值/折扣';
COMMENT ON COLUMN "public"."asm_coupons"."min_amount" IS '最低使用金额';
COMMENT ON COLUMN "public"."asm_coupons"."start_time" IS '生效时间';
COMMENT ON COLUMN "public"."asm_coupons"."end_time" IS '过期时间';
COMMENT ON COLUMN "public"."asm_coupons"."status" IS '使用状态';
COMMENT ON COLUMN "public"."asm_coupons"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_coupons"."used_time" IS '使用时间';
COMMENT ON COLUMN "public"."asm_coupons"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_coupons"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_coupons" IS '优惠券表';

-- ----------------------------
-- Records of asm_coupons
-- ----------------------------

-- ----------------------------
-- Table structure for asm_course
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_course";
CREATE TABLE "public"."asm_course" (
  "id" int4 NOT NULL DEFAULT nextval('asm_course_id_seq'::regclass),
  "title" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "price" float8,
  "cover_image" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "type" "public"."course_type_enum",
  "is_recommended" bool,
  "teacher_id" int4,
  "poster_url" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."asm_course"."id" IS '课程ID';
COMMENT ON COLUMN "public"."asm_course"."title" IS '课程标题';
COMMENT ON COLUMN "public"."asm_course"."description" IS '课程描述';
COMMENT ON COLUMN "public"."asm_course"."price" IS '价格';
COMMENT ON COLUMN "public"."asm_course"."cover_image" IS '课程封面图片URL';
COMMENT ON COLUMN "public"."asm_course"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_course"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_course"."type" IS '课程类型:sy-商业IP,ch-出海,yx-营销,trz-投融资,gl-管理,yy-AI应用';
COMMENT ON COLUMN "public"."asm_course"."is_recommended" IS '是否为推荐课程';
COMMENT ON COLUMN "public"."asm_course"."teacher_id" IS '关联导师ID';
COMMENT ON COLUMN "public"."asm_course"."poster_url" IS '海报图片URL';
COMMENT ON TABLE "public"."asm_course" IS '课程信息表';

-- ----------------------------
-- Records of asm_course
-- ----------------------------
INSERT INTO "public"."asm_course" VALUES (64, '人人都能用好Deepseek', '破除AI神秘感，1小时搞懂技术本质与核心优势。 ', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598856235-e7b34dda0fbf15b5e33ff751897e8f1.png', '2025-03-10 08:40:03.229', '2025-03-26 18:05:09.291', 'LQGX', 't', 19, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598848308-e319bb0798fce854a862e6ed2d60dee.png');
INSERT INTO "public"."asm_course" VALUES (5, '小马宋·营销实战升级', '营销大咖小马宋以实战案例拆解底层逻辑，帮你构建知识体系，实现业绩增长。
', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739762714518-WechatIMG242.jpg', '2024-12-28 07:06:03.731663', '2025-03-02 01:40:34.066', 'WXXL', 't', 5, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1738896500546-1738835172283-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250206174559.png');
INSERT INTO "public"."asm_course" VALUES (4, '小马宋·餐饮企业的商业洞察与营销实践', '剖析商业本质，分享实战案例与落地营销法，助力实现品牌崛起与业绩增长 。
', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739762872034-WechatIMG244.jpg', '2024-12-28 07:06:03.731663', '2025-03-02 01:38:21.494', 'QLSD', 't', 5, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1738835172283-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250206174559.png');
INSERT INTO "public"."asm_course" VALUES (3, '微信视频号的商业玩法', '微信视频号介绍及商业玩法', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739767004653-%E6%9C%AA%E5%91%BD%E5%90%8D__2025-02-17%2B12_36_26.jpg', '2024-12-28 07:06:03.731663', '2025-03-04 02:16:24.814', 'JWH', 't', 2, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739767957065-WechatIMG7522.jpg');
INSERT INTO "public"."asm_course" VALUES (2, '股权激励与合伙人设计', '解锁股权激励密码，精研合伙人设计，开启财富共赢。', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739767929477-%E6%9C%AA%E5%91%BD%E5%90%8D__2025-02-17%2B12_51_57.jpg', '2024-12-28 07:06:03.731663', '2025-03-02 01:40:03.916', 'LQKY', 't', 3, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1738834418429-%E8%87%A7%E5%85%B6%E8%B6%85.jpeg');
INSERT INTO "public"."asm_course" VALUES (7, '人人都能用好Deepseek', '破除AI神秘感，1小时搞懂技术本质与核心优势。 ', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598856235-e7b34dda0fbf15b5e33ff751897e8f1.png', '2025-03-10 08:40:03.229', '2025-04-03 04:28:50.868', 'QLYJ', 'f', 19, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598848308-e319bb0798fce854a862e6ed2d60dee.png');
INSERT INTO "public"."asm_course" VALUES (72, '内经天机：五维解码生命智慧', '黄老通书：藏在医经里的文明源代码
《内经商纬：五行战略与生命管理学》
《黄帝问道：多维智慧通鉴录》', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743760394915-%E6%B8%A9%E4%BC%9F%E5%BC%BA%20(1).png', '2025-04-04 09:54:53.933', '2025-04-04 09:56:25.113', 'LQGX', 't', 23, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743760548080-%E6%B8%A9%E4%BC%9F%E5%BC%BA.png');
INSERT INTO "public"."asm_course" VALUES (71, '笔划里的中国：解读古文字密码', '解答古文字结构中的密码：立“人”成“仁”', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743758438048-%E5%BE%90%E5%B0%8F%E6%95%8F%E8%AF%BE%E7%A8%8B%E5%9B%BE.jfif', '2025-04-04 06:20:32.948', '2025-04-04 09:20:55.851', 'LTJX', 't', 22, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743770138775-%E5%BE%90%E5%B0%8F%E6%95%8F%E6%A0%8F%E7%9B%AE.png');
INSERT INTO "public"."asm_course" VALUES (1, '打造360°全面预算系统', '打破业财壁垒，深度融合财务与业务，重塑企业核心竞争力。', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739766948377-%E6%9C%AA%E5%91%BD%E5%90%8D__2025-02-17%2B12_35_23.jpg', '2024-12-28 07:06:03.731663', '2025-03-02 01:39:50.817', 'LTJX', 't', 4, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1738834318755-%E5%BC%A0%E5%9F%B9.jpg');
INSERT INTO "public"."asm_course" VALUES (6, '人人都能用好Deepseek', '破除AI神秘感，1小时搞懂技术本质与核心优势。 ', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598856235-e7b34dda0fbf15b5e33ff751897e8f1.png', '2025-03-10 08:40:03.229', '2025-04-15 11:50:58.932805', 'LYWY', 'f', 1, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598848308-e319bb0798fce854a862e6ed2d60dee.png');
INSERT INTO "public"."asm_course" VALUES (73, '1', '1', 0.01, 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744694686-ai.jpg', '2025-04-15 13:24:50.276144', '2025-04-15 18:17:25.300456', 'WXXL', 't', NULL, 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744694688-澜沁国学.png');

-- ----------------------------
-- Table structure for asm_course_section
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_course_section";
CREATE TABLE "public"."asm_course_section" (
  "id" int4 NOT NULL DEFAULT nextval('asm_course_section_id_seq'::regclass),
  "title" varchar COLLATE "pg_catalog"."default",
  "duration" int4,
  "order" int4,
  "is_free" bool,
  "video_url" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "is_published" bool,
  "course_id" int4
)
;
COMMENT ON COLUMN "public"."asm_course_section"."id" IS '章节ID';
COMMENT ON COLUMN "public"."asm_course_section"."title" IS '章节标题';
COMMENT ON COLUMN "public"."asm_course_section"."duration" IS '时长(秒)';
COMMENT ON COLUMN "public"."asm_course_section"."order" IS '排序';
COMMENT ON COLUMN "public"."asm_course_section"."is_free" IS '是否免费';
COMMENT ON COLUMN "public"."asm_course_section"."video_url" IS '视频URL';
COMMENT ON COLUMN "public"."asm_course_section"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_course_section"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_course_section"."is_published" IS '是否发布';
COMMENT ON COLUMN "public"."asm_course_section"."course_id" IS '关联课程ID';
COMMENT ON TABLE "public"."asm_course_section" IS '课程章节表';

-- ----------------------------
-- Records of asm_course_section
-- ----------------------------
INSERT INTO "public"."asm_course_section" VALUES (48, '01.AIGC到底是什么？一节课带你了解这一技术革命的核心！', 710, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741596344888-01.AIGC%E5%88%B0%E5%BA%95%E6%98%AF%E4%BB%80%E4%B9%88%EF%BC%9F%E4%B8%80%E8%8A%82%E8%AF%BE%E5%B8%A6%E4%BD%A0%E4%BA%86%E8%A7%A3%E8%BF%99%E4%B8%80%E6%8A%80%E6%9C%AF%E9%9D%A9%E5%91%BD%E7%9A%84%E6%A0%B8%E5%BF%83%EF%BC%81.mp4', '2025-03-10 09:06:01.761', '2025-03-26 18:12:29.718', 't', 64);
INSERT INTO "public"."asm_course_section" VALUES (49, '02.巨人肩膀上的deepseek -认知篇', 700, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741597646121-02.%E5%B7%A8%E4%BA%BA%E8%82%A9%E8%86%80%E4%B8%8A%E7%9A%84deepseek%20-%E8%AE%A4%E7%9F%A5%E7%AF%87.mp4', '2025-03-10 09:17:13.616', '2025-03-26 18:12:32.917', 't', 64);
INSERT INTO "public"."asm_course_section" VALUES (50, '03.巨人肩膀上的deepseek- 技术揭秘', 0, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598291386-03.%E5%B7%A8%E4%BA%BA%E8%82%A9%E8%86%80%E4%B8%8A%E7%9A%84deepseek-%20%E6%8A%80%E6%9C%AF%E6%8F%AD%E7%A7%98.mp4', '2025-03-10 09:25:21.11', '2025-03-26 18:12:35.515', 't', 64);
INSERT INTO "public"."asm_course_section" VALUES (47, '第九章：股权激励实施的4大雷区', 1140, 9, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737602040723-09%E3%80%81%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E5%AE%9E%E6%96%BD%E7%9A%844%E5%A4%A7%E9%9B%B7%E5%8C%BA.mp4', '2025-01-23 03:17:53.277', '2025-01-23 03:17:53.277', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (46, '第八章：股权治理', 1140, 8, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737601780766-08%E3%80%81%E8%82%A1%E6%9D%83%E6%B2%BB%E7%90%86.mp4', '2025-01-23 03:13:08.122', '2025-01-23 03:13:08.122', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (45, '第七章：股权激励方案设计', 3240, 7, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737601128091-07%E3%80%81%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E6%96%B9%E6%A1%88%E8%AE%BE%E8%AE%A1.mp4', '2025-01-23 03:09:02.722', '2025-01-23 03:09:02.722', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (44, '第六章：股权模式灵活应用', 1800, 6, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737600687583-06%E3%80%81%E8%82%A1%E6%9D%83%E6%A8%A1%E5%BC%8F%E7%81%B5%E6%B4%BB%E5%BA%94%E7%94%A8.mp4', '2025-01-23 02:56:58.086', '2025-01-23 02:56:58.086', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (43, '第五章：股权激励常用模式', 3900, 5, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737599903454-05%E3%80%81%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E5%B8%B8%E7%94%A8%E6%A8%A1%E5%BC%8F.mp4', '2025-01-23 02:50:09.911', '2025-01-23 02:50:09.911', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (42, '第四章：股权激励原则', 2340, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737599026227-04%E3%80%81%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E5%8E%9F%E5%88%99.mp4', '2025-01-23 02:34:16.341', '2025-01-23 02:34:16.341', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (41, '第三章：股权相关认知', 1020, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737598669926-03%E3%80%81%E8%82%A1%E6%9D%83%E7%9B%B8%E5%85%B3%E8%AE%A4%E7%9F%A5.mp4', '2025-01-23 02:21:18.176', '2025-01-23 02:34:31.145', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (40, '第二章：资本时代为何要股改？', 600, 2, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737598246760-02%E3%80%81%E8%B5%84%E6%9C%AC%E6%97%B6%E4%BB%A3%E4%B8%BA%E4%BD%95%E8%A6%81%E8%82%A1%E6%94%B9.mp4', '2025-01-23 02:15:35.912', '2025-01-23 02:15:51.48', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (39, '第一章：转型时代为何一定要做股改？', 1080, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737597958302-01%E3%80%81%E8%BD%AC%E5%9E%8B%E6%97%B6%E4%BB%A3%E4%B8%BA%E4%BD%95%E4%B8%80%E5%AE%9A%E8%A6%81%E5%81%9A%E8%82%A1%E6%94%B9.mp4', '2025-01-23 02:09:58.161', '2025-01-23 02:09:58.161', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (38, '第十四章：如何取得卓越的经营业绩？', 1560, 14, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737548172531-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC14%E7%AB%A0%20%E5%A6%82%E4%BD%95%E5%8F%96%E5%BE%97%E5%8D%93%E8%B6%8A%E7%9A%84%E7%BB%8F%E8%90%A5%E4%B8%9A%E7%BB%A9_ev_ev.mp4', '2025-01-22 12:22:46.842', '2025-01-22 12:22:46.842', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (37, '第十三章：重新审视营销', 1320, 13, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737547794678-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC13%E7%AB%A0%20%E9%87%8D%E6%96%B0%E5%AE%A1%E8%A7%86%E8%90%A5%E9%94%80_ev_ev.mp4', '2025-01-22 12:15:02.363', '2025-01-22 12:15:02.363', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (36, '第十二章：做营销一定要花钱吗?', 960, 12, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737547463231-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC12%E7%AB%A0%20%E5%81%9A%E8%90%A5%E9%94%80%E4%B8%80%E5%AE%9A%E8%A6%81%E8%8A%B1%E9%92%B1%E5%90%97%EF%BC%9F_ev_ev.mp4', '2025-01-22 12:08:32.035', '2025-01-22 12:08:32.035', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (35, '第十一章：写出有效的口号', 1080, 11, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737547174781-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC11%E7%AB%A0%20%E5%86%99%E5%87%BA%E6%9C%89%E6%95%88%E7%9A%84%E5%8F%A3%E5%8F%B7_ev_ev.mp4', '2025-01-22 12:03:43.963', '2025-01-22 12:03:43.963', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (34, '第十章：如何设计一家生意兴隆的店铺2.0', 780, 10, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737546956589-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC10%E7%AB%A0%20%E5%A6%82%E4%BD%95%E8%AE%BE%E8%AE%A1%E4%B8%80%E5%AE%B6%E7%94%9F%E6%84%8F%E5%85%B4%E9%9A%86%E7%9A%84%E5%BA%97%E9%93%BA2.0%EF%BC%9F_ev_ev.mp4', '2025-01-22 11:58:55.678', '2025-01-22 12:08:54.177', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (33, '第九章：如何设计一家生意兴隆的店铺1.0', 1080, 9, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737546644907-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC9%E7%AB%A0%20%E5%A6%82%E4%BD%95%E8%AE%BE%E8%AE%A1%E4%B8%80%E5%AE%B6%E7%94%9F%E6%84%8F%E5%85%B4%E9%9A%86%E7%9A%84%E5%BA%97%E9%93%BA1.0%EF%BC%9F_ev_ev.mp4', '2025-01-22 11:55:03.789', '2025-01-22 12:09:05.183', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (32, '第八章：用超级符号降低成本', 1080, 8, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737546279877-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC8%E7%AB%A0%20%E7%94%A8%E8%B6%85%E7%BA%A7%E7%AC%A6%E5%8F%B7%E9%99%8D%E4%BD%8E%E6%88%90%E6%9C%AC_ev_ev.mp4', '2025-01-22 11:48:38.01', '2025-01-22 11:48:38.01', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (31, ' 第七章：定价与精益创业', 540, 7, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737546038286-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC7%E7%AB%A0%20%E5%AE%9A%E4%BB%B7%E4%B8%8E%E7%B2%BE%E7%9B%8A%E5%88%9B%E4%B8%9A_ev_ev.mp4', '2025-01-22 11:43:48.419', '2025-01-22 11:43:48.419', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (30, '第六章：基于顾客视角的几种定价方法 ', 840, 6, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737545812965-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC6%E7%AB%A0%20%E5%9F%BA%E4%BA%8E%E9%A1%BE%E5%AE%A2%E8%A7%86%E8%A7%92%E7%9A%84%E5%87%A0%E7%A7%8D%E5%AE%9A%E4%BB%B7%E6%96%B9%E6%B3%95_ev_ev.mp4', '2025-01-22 11:40:13.638', '2025-01-22 11:40:13.638', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (29, '第五章：包装设计的本质', 1380, 5, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737545443433-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC5%E7%AB%A0%20%E5%8C%85%E8%A3%85%E8%AE%BE%E8%AE%A1%E7%9A%84%E6%9C%AC%E8%B4%A8_ev_ev.mp4', '2025-01-22 11:35:55.615', '2025-01-22 11:35:55.615', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (28, '第四章：JTBD理论，及顾客视角 ', 1140, 4, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737545077536-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC4%E7%AB%A0%20JTBD%E7%90%86%E8%AE%BA%EF%BC%8C%E5%8F%8A%E9%A1%BE%E5%AE%A2%E8%A7%86%E8%A7%92_ev_ev.mp4', '2025-01-22 11:29:36.015', '2025-01-22 11:29:53.08', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (27, '第三章：名字取得好，公司上市早 ', 1140, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737544727558-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC3%E7%AB%A0%20%E5%90%8D%E5%AD%97%E5%8F%96%E5%BE%97%E5%A5%BD%EF%BC%8C%E5%85%AC%E5%8F%B8%E4%B8%8A%E5%B8%82%E6%97%A9_ev_ev.mp4', '2025-01-22 11:23:04.46', '2025-01-22 11:23:15.104', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (26, '第二章：选个好行业，做个大企业', 1860, 2, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737544248254-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC2%E7%AB%A0%20%E9%80%89%E4%B8%AA%E5%A5%BD%E8%A1%8C%E4%B8%9A%EF%BC%8C%E5%81%9A%E4%B8%AA%E5%A4%A7%E4%BC%81%E4%B8%9A_ev_ev.mp4', '2025-01-22 11:17:49.913', '2025-01-22 11:17:49.913', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (25, '第一章：价值成本原理', 2340, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737543212526-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC1%E7%AB%A0%20%E4%BB%B7%E5%80%BC%E6%88%90%E6%9C%AC%E5%8E%9F%E7%90%86_ev_ev.mp4', '2025-01-22 11:05:31', '2025-01-22 11:08:56.243', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (24, '第五讲：从罗辑思维到得到的思维模型', 1020, 5, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737542535996-05%E8%AE%B2_%20%E4%BB%8E%E7%BD%97%E8%BE%91%E6%80%9D%E7%BB%B4%E5%88%B0%E5%BE%97%E5%88%B0%E7%9A%84%E6%80%9D%E7%BB%B4%E6%A8%A1%E5%9E%8B.mp4', '2025-01-22 10:45:43.808', '2025-01-22 10:45:43.808', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (23, '第四讲：太二酸菜鱼和品牌人格塑造', 2580, 4, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737541833447-04%E8%AE%B2_%20%E5%A4%AA%E4%BA%8C%E9%85%B8%E8%8F%9C%E9%B1%BC%E5%92%8C%E5%93%81%E7%89%8C%E4%BA%BA%E6%A0%BC%E5%A1%91%E9%80%A0.mp4', '2025-01-22 10:39:37.08', '2025-01-22 10:41:05.043', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (22, '第三讲：熊猫不走和消费戏剧', 3600, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737541005358-03%E8%AE%B2_%20%E7%86%8A%E7%8C%AB%E4%B8%8D%E8%B5%B0%E5%92%8C%E6%B6%88%E8%B4%B9%E6%88%8F%E5%89%A7.mp4', '2025-01-22 10:28:49.98', '2025-01-22 10:40:41.689', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (21, '第二讲：从西贝和云海肴看4P', 3240, 2, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737540249075-02%E8%AE%B2_%20%E4%BB%8E%E8%A5%BF%E8%B4%9D%E5%92%8C%E4%BA%91%E6%B5%B7%E8%82%B4%E7%9C%8B4P.mp4', '2025-01-22 10:14:38.459', '2025-01-22 10:40:25.878', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (20, '第一讲：古茗 战略无人区中的生意', 5280, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737538641675-01%E8%AE%B2_%20%E5%8F%A4%E8%8C%97%EF%BC%9A%E6%88%98%E7%95%A5%E6%97%A0%E4%BA%BA%E5%8C%BA%E4%B8%AD%E7%9A%84%E7%94%9F%E6%84%8F.mp4', '2025-01-22 10:00:48.663', '2025-01-22 10:40:12.408', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (19, '无人直播-第十章', 28, 10, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737457089911-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD10.mp4', '2025-01-21 10:58:37.276', '2025-01-23 03:46:08.988', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (18, '无人直播-第九章', 34, 9, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737457038287-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD9.mp4', '2025-01-21 10:57:50.093', '2025-01-23 03:45:52.435', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (17, '无人直播-第八章', 36, 8, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456999590-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD8.mp4', '2025-01-21 10:57:01.977', '2025-01-21 11:01:26.054', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (16, '无人直播-第七章', 30, 7, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456948518-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD7.mp4', '2025-01-21 10:56:06.814', '2025-01-23 03:45:36.657', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (15, '无人直播-第六章', 25, 6, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456820341-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD6.mp4', '2025-01-21 10:55:17.003', '2025-01-23 03:45:23.627', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (14, '无人直播-第五章', 20, 5, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456699777-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD5.mp4', '2025-01-21 10:53:17.422', '2025-01-21 11:00:48.294', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (13, '无人直播-第四章', 21, 4, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456649876-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD4.mp4', '2025-01-21 10:51:13.022', '2025-01-23 03:45:07.672', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (12, '无人直播-第三章', 31, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456579766-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD3.mp4', '2025-01-21 10:50:03.428', '2025-01-21 11:00:58.186', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (11, '无人直播-第二章', 21, 2, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456476459-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD2.mp4', '2025-01-21 10:48:16.215', '2025-01-23 03:44:46.563', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (10, '无人直播-第一章', 22, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456374993-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD1.mp4', '2025-01-21 10:47:05.008', '2025-01-23 03:44:28.263', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (3, '打造360°全面预算系统-下', 4020, 2, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737549343656-%E4%B8%9A%E8%B4%A2%E8%9E%8D%E5%90%882-%E5%AD%9F%E9%83%A1%E8%80%81%E5%B8%88.mp4', '2024-12-29 05:54:56.898429', '2025-01-23 03:43:58.841', 't', 1);
INSERT INTO "public"."asm_course_section" VALUES (53, '03.巨人肩膀上的deepseek- 技术揭秘', 0, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598291386-03.%E5%B7%A8%E4%BA%BA%E8%82%A9%E8%86%80%E4%B8%8A%E7%9A%84deepseek-%20%E6%8A%80%E6%9C%AF%E6%8F%AD%E7%A7%98.mp4', '2025-03-10 09:25:21.11', '2025-03-26 18:12:35.515', 't', 6);
INSERT INTO "public"."asm_course_section" VALUES (52, '02.巨人肩膀上的deepseek -认知篇', 700, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741597646121-02.%E5%B7%A8%E4%BA%BA%E8%82%A9%E8%86%80%E4%B8%8A%E7%9A%84deepseek%20-%E8%AE%A4%E7%9F%A5%E7%AF%87.mp4', '2025-03-10 09:17:13.616', '2025-03-26 18:12:32.917', 't', 6);
INSERT INTO "public"."asm_course_section" VALUES (51, '01.AIGC到底是什么？一节课带你了解这一技术革命的核心！', 710, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741596344888-01.AIGC%E5%88%B0%E5%BA%95%E6%98%AF%E4%BB%80%E4%B9%88%EF%BC%9F%E4%B8%80%E8%8A%82%E8%AF%BE%E5%B8%A6%E4%BD%A0%E4%BA%86%E8%A7%A3%E8%BF%99%E4%B8%80%E6%8A%80%E6%9C%AF%E9%9D%A9%E5%91%BD%E7%9A%84%E6%A0%B8%E5%BF%83%EF%BC%81.mp4', '2025-03-10 09:06:01.761', '2025-03-26 18:12:29.718', 't', 6);
INSERT INTO "public"."asm_course_section" VALUES (56, '03.巨人肩膀上的deepseek- 技术揭秘', 0, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598291386-03.%E5%B7%A8%E4%BA%BA%E8%82%A9%E8%86%80%E4%B8%8A%E7%9A%84deepseek-%20%E6%8A%80%E6%9C%AF%E6%8F%AD%E7%A7%98.mp4', '2025-03-10 09:25:21.11', '2025-03-26 18:12:35.515', 't', 7);
INSERT INTO "public"."asm_course_section" VALUES (55, '02.巨人肩膀上的deepseek -认知篇', 700, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741597646121-02.%E5%B7%A8%E4%BA%BA%E8%82%A9%E8%86%80%E4%B8%8A%E7%9A%84deepseek%20-%E8%AE%A4%E7%9F%A5%E7%AF%87.mp4', '2025-03-10 09:17:13.616', '2025-03-26 18:12:32.917', 't', 7);
INSERT INTO "public"."asm_course_section" VALUES (54, '01.AIGC到底是什么？一节课带你了解这一技术革命的核心！', 710, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741596344888-01.AIGC%E5%88%B0%E5%BA%95%E6%98%AF%E4%BB%80%E4%B9%88%EF%BC%9F%E4%B8%80%E8%8A%82%E8%AF%BE%E5%B8%A6%E4%BD%A0%E4%BA%86%E8%A7%A3%E8%BF%99%E4%B8%80%E6%8A%80%E6%9C%AF%E9%9D%A9%E5%91%BD%E7%9A%84%E6%A0%B8%E5%BF%83%EF%BC%81.mp4', '2025-03-10 09:06:01.761', '2025-03-26 18:12:29.718', 't', 7);
INSERT INTO "public"."asm_course_section" VALUES (57, '1', 76, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743763630292-4.mp4', '2025-04-04 10:48:08.48', '2025-04-04 10:48:08.48', 't', 1);
INSERT INTO "public"."asm_course_section" VALUES (58, '2', 76, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743763710330-4.mp4', '2025-04-04 10:48:36.569', '2025-04-04 10:48:36.569', 't', 1);
INSERT INTO "public"."asm_course_section" VALUES (2, '打造360°全面预算系统-上', 5340, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737548686799-%E4%B8%9A%E8%B4%A2%E8%9E%8D%E5%90%881-%E5%AD%9F%E9%83%A1%E8%80%81%E5%B8%88.mp4', '2024-12-29 05:54:56.898429', '2025-04-09 08:51:52.802', 't', 1);
INSERT INTO "public"."asm_course_section" VALUES (61, '徐老师中国古汉字教育法第三节-仁字', 732, 3, 't', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744214393532-%E5%BE%90%E8%80%81%E5%B8%88%E4%B8%AD%E5%9B%BD%E5%8F%A4%E6%B1%89%E5%AD%97%E6%95%99%E8%82%B2%E6%B3%95%E7%AC%AC%E4%B8%89%E8%8A%82-%E4%BB%81%E5%AD%97.mp4', '2025-04-09 16:48:03.707', '2025-04-09 16:48:03.707', 't', 71);
INSERT INTO "public"."asm_course_section" VALUES (59, '徐老师中国古汉字教育法第一节-课程介绍', 696, 1, 't', 'https://ai-startup-mentor.oss-cn-hangzhou.aliyuncs.com/%E5%A4%A7%E6%96%87%E4%BB%B6/%E5%BE%90%E8%80%81%E5%B8%88%E4%B8%AD%E5%9B%BD%E5%8F%A4%E6%B1%89%E5%AD%97%E6%95%99%E8%82%B2%E6%B3%95%E7%AC%AC%E4%B8%80%E8%8A%82-%E8%AF%BE%E7%A8%8B%E4%BB%8B%E7%BB%8D.mp4', '2025-04-09 16:46:12.76', '2025-04-09 16:46:12.76', 't', 71);
INSERT INTO "public"."asm_course_section" VALUES (60, '徐老师中国古汉字教育法第二节-人字', 600, 2, 't', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744213818858-%E5%BE%90%E8%80%81%E5%B8%88%E4%B8%AD%E5%9B%BD%E5%8F%A4%E6%B1%89%E5%AD%97%E6%95%99%E8%82%B2%E6%B3%95%E7%AC%AC%E4%BA%8C%E8%8A%82-%E4%BA%BA%E5%AD%97.mp4', '2025-04-09 16:46:57.658', '2025-04-09 16:46:57.658', 't', 71);
INSERT INTO "public"."asm_course_section" VALUES (62, '1', 1, 0, 't', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744788475-4.mp4', '2025-04-16 15:28:02.077555', '2025-04-16 15:28:02.077573', 't', 64);
INSERT INTO "public"."asm_course_section" VALUES (63, '1', 1, 0, 't', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744787336-徐老师中国古汉字教育法第二节-人字.mp4', '2025-04-16 15:28:12.347505', '2025-04-16 15:28:12.347531', 't', 64);

-- ----------------------------
-- Table structure for asm_investment_show
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_investment_show";
CREATE TABLE "public"."asm_investment_show" (
  "id" int4 NOT NULL DEFAULT nextval('asm_investment_show_id_seq'::regclass),
  "episode" int4,
  "title" varchar COLLATE "pg_catalog"."default",
  "poster_url" varchar COLLATE "pg_catalog"."default",
  "video_url" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "guest_info" json,
  "views_count" int4,
  "is_published" bool,
  "publish_time" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_investment_show"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_investment_show"."episode" IS '期号';
COMMENT ON COLUMN "public"."asm_investment_show"."title" IS '标题';
COMMENT ON COLUMN "public"."asm_investment_show"."poster_url" IS '海报图片URL';
COMMENT ON COLUMN "public"."asm_investment_show"."video_url" IS '视频URL';
COMMENT ON COLUMN "public"."asm_investment_show"."description" IS '节目描述';
COMMENT ON COLUMN "public"."asm_investment_show"."guest_info" IS '嘉宾信息';
COMMENT ON COLUMN "public"."asm_investment_show"."views_count" IS '观看次数';
COMMENT ON COLUMN "public"."asm_investment_show"."is_published" IS '是否发布';
COMMENT ON COLUMN "public"."asm_investment_show"."publish_time" IS '发布时间';
COMMENT ON COLUMN "public"."asm_investment_show"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_investment_show"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_investment_show" IS '创投show历史记录表';

-- ----------------------------
-- Records of asm_investment_show
-- ----------------------------
INSERT INTO "public"."asm_investment_show" VALUES (1, 1, 'NewMoney湖畔Show', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737445298375-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250121154128.jpg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737444671516-%E6%B9%96%E7%95%94%E7%A7%80.mp4', '湖畔 SHOW 企业家商业 IP 公开课震撼来袭！汇聚行业大咖导师，包括黑总王博轩、许云峰、Mark 等大咖云集，9月5日于杭州湖畔创研中心开启会员公开课，剖析商业IP生意逻辑、流量真相，以及 NewMoney 新物种系列发布会，次日更有修然控股企业访学，深度交流学习，助你洞悉商业奥秘，开启创业新征程！', '"主讲导师：\nNewMoney撕董会主理人——黑总\n演讲主题：【祛魅—商业IP流量和新钱的真相】\n\n嘉宾导师：\n直男财经创始人、全网粉丝超4000万——许云峰\n演讲主题：【打造IP，提升品牌影响力】\n\n嘉宾导师：\n天使投资人、浙江行早董事长——Mark\n演讲主题：【以终局思考现在，痛点永远是机会】\n\n特别环节：赫畅脱口秀首秀\n主题：【关于创业的假象】"', 1200, 't', '2023-12-01 20:00:00', '2023-11-25 10:00:00', '2025-01-21 07:47:37.469');
INSERT INTO "public"."asm_investment_show" VALUES (2, 2, 'NewMoney创投Show', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737445682980-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250121151634.jpg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737446024979-%E5%88%9B%E6%8A%95%E7%A7%80.mp4', 'NewMoney创投SHOW与知名大咖投资人深度交流，获项目指引与融资机会；链接高净值 NewMoney 会员，打通上下游资源，实现跨行业学习；携手 NewMoney 黑总及大咖，共掘行业机遇，探寻红利与新钱方向。', '"超强导师阵容震撼登场！\nNewMoney 撕董会主理人黑总，带你洞察商业风云；\n\n梅花创投创始合伙人吴世春，以独到眼光挖掘财富密码；\n\n金慧丰投资董事长、天使联合汇主席周丽霞，带来丰富的投资经验与智慧；\n\n坚果创投董事长王展，开启智慧投资之门。"', 1500, 't', '2023-12-15 20:00:00', '2023-12-10 10:00:00', '2025-01-21 08:01:21.88');

-- ----------------------------
-- Table structure for asm_investment_show_application
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_investment_show_application";
CREATE TABLE "public"."asm_investment_show_application" (
  "id" int4 NOT NULL DEFAULT nextval('asm_investment_show_application_id_seq'::regclass),
  "company_name" varchar COLLATE "pg_catalog"."default",
  "industry" varchar COLLATE "pg_catalog"."default",
  "business" varchar COLLATE "pg_catalog"."default",
  "product" varchar COLLATE "pg_catalog"."default",
  "founder" varchar COLLATE "pg_catalog"."default",
  "team" varchar COLLATE "pg_catalog"."default",
  "attachments" json,
  "status" varchar COLLATE "pg_catalog"."default",
  "user_id" int4,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_investment_show_application"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_investment_show_application"."company_name" IS '公司介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."industry" IS '所处行业';
COMMENT ON COLUMN "public"."asm_investment_show_application"."business" IS '业务介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."product" IS '产品介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."founder" IS '创始人介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."team" IS '团队介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."attachments" IS '附件列表 [{name: "文件名", url: "文件链接"}]';
COMMENT ON COLUMN "public"."asm_investment_show_application"."status" IS '状态:PENDING-待审核,APPROVED-已通过,REJECTED-已拒绝';
COMMENT ON COLUMN "public"."asm_investment_show_application"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_investment_show_application"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_investment_show_application"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_investment_show_application" IS '创投show报名表';

-- ----------------------------
-- Records of asm_investment_show_application
-- ----------------------------

-- ----------------------------
-- Table structure for asm_orders
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_orders";
CREATE TABLE "public"."asm_orders" (
  "id" int4 NOT NULL DEFAULT nextval('asm_orders_id_seq'::regclass),
  "order_no" varchar COLLATE "pg_catalog"."default",
  "user_id" int4,
  "amount" float8,
  "status" "public"."order_status_enum",
  "order_type" "public"."order_type_enum",
  "course_id" int4,
  "agent_id" int4,
  "teacher_id" int4,
  "appointment_hour" int4,
  "contact_name" varchar COLLATE "pg_catalog"."default",
  "contact_phone" varchar COLLATE "pg_catalog"."default",
  "months" int4,
  "coupon_id" int4,
  "pay_time" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_orders"."id" IS '订单ID';
COMMENT ON COLUMN "public"."asm_orders"."order_no" IS '订单编号';
COMMENT ON COLUMN "public"."asm_orders"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_orders"."amount" IS '订单金额';
COMMENT ON COLUMN "public"."asm_orders"."status" IS '订单状态';
COMMENT ON COLUMN "public"."asm_orders"."order_type" IS '订单类型';
COMMENT ON COLUMN "public"."asm_orders"."course_id" IS '课程ID';
COMMENT ON COLUMN "public"."asm_orders"."agent_id" IS '智能体ID';
COMMENT ON COLUMN "public"."asm_orders"."teacher_id" IS '老师ID';
COMMENT ON COLUMN "public"."asm_orders"."appointment_hour" IS '预约小时数';
COMMENT ON COLUMN "public"."asm_orders"."contact_name" IS '联系人';
COMMENT ON COLUMN "public"."asm_orders"."contact_phone" IS '联系方式';
COMMENT ON COLUMN "public"."asm_orders"."months" IS '购买月数';
COMMENT ON COLUMN "public"."asm_orders"."coupon_id" IS '优惠券ID';
COMMENT ON COLUMN "public"."asm_orders"."pay_time" IS '支付时间';
COMMENT ON COLUMN "public"."asm_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_orders"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_orders" IS '统一订单表';

-- ----------------------------
-- Records of asm_orders
-- ----------------------------
INSERT INTO "public"."asm_orders" VALUES (6, 'COURSE_1745724923_38', 38, 0.01, 'PAID', 'COURSE', 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-04-27 11:36:07.159021', '2025-04-27 11:35:23.55606', '2025-04-27 11:34:27.841223');

-- ----------------------------
-- Table structure for asm_system_parameters
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_system_parameters";
CREATE TABLE "public"."asm_system_parameters" (
  "id" int4 NOT NULL DEFAULT nextval('asm_system_parameters_id_seq'::regclass),
  "key" varchar COLLATE "pg_catalog"."default",
  "value" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_system_parameters"."id" IS '参数ID';
COMMENT ON COLUMN "public"."asm_system_parameters"."key" IS '参数键';
COMMENT ON COLUMN "public"."asm_system_parameters"."value" IS '参数值';
COMMENT ON COLUMN "public"."asm_system_parameters"."description" IS '参数描述';
COMMENT ON COLUMN "public"."asm_system_parameters"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_system_parameters"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_system_parameters" IS '系统参数表';

-- ----------------------------
-- Records of asm_system_parameters
-- ----------------------------
INSERT INTO "public"."asm_system_parameters" VALUES (2, 'course_banner', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744689083-澜沁国学.png', '课程页 Banner 图片 URL', '2025-01-20 08:14:58.080845', '2025-03-25 06:16:37.804');

-- ----------------------------
-- Table structure for asm_teacher
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_teacher";
CREATE TABLE "public"."asm_teacher" (
  "id" int4 NOT NULL DEFAULT nextval('asm_teacher_id_seq'::regclass),
  "name" varchar COLLATE "pg_catalog"."default",
  "avatar" varchar COLLATE "pg_catalog"."default",
  "hourly_rate" int4,
  "description" varchar COLLATE "pg_catalog"."default",
  "tags" json,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "poster" varchar COLLATE "pg_catalog"."default",
  "assistant_qrcode" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."asm_teacher"."id" IS '导师ID';
COMMENT ON COLUMN "public"."asm_teacher"."name" IS '导师姓名';
COMMENT ON COLUMN "public"."asm_teacher"."avatar" IS '导师头像URL';
COMMENT ON COLUMN "public"."asm_teacher"."hourly_rate" IS '每小时收费(人民币元)';
COMMENT ON COLUMN "public"."asm_teacher"."description" IS '导师描述信息';
COMMENT ON COLUMN "public"."asm_teacher"."tags" IS '标签列表，如：["人气导师", "从业年丰富"]';
COMMENT ON COLUMN "public"."asm_teacher"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_teacher"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_teacher"."poster" IS '导师海报图URL';
COMMENT ON COLUMN "public"."asm_teacher"."assistant_qrcode" IS '助理二维码URL';
COMMENT ON TABLE "public"."asm_teacher" IS '导师信息表';

-- ----------------------------
-- Records of asm_teacher
-- ----------------------------
INSERT INTO "public"."asm_teacher" VALUES (1, '吴世春', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737447120392-1737438243999-397d0dae815db6acc393b5ee79d2144f.jpeg', 50000, '梅花创投创始合伙人，知名天使投资人。曾投资大掌门创造了1500倍回报；投资趣店获得了超过1000倍回报，并且连年荣获清科、投中、《财富》等权威第三方机构评选中国天使投资人10强、中国影响力的30位投资人等荣誉。
', '["投资专家","创业导师","资深导师"]', '2024-12-28 06:59:16.218299', '2025-02-17 02:32:54.32', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737438253338-%E5%90%B4%E4%B8%96%E6%98%A5.jpeg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737438261875-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (5, '小马宋', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737447400386-1737437834462-%E5%B0%8F%E9%A9%AC%E5%AE%8B.jpg', 30000, '独立战略营销顾问，罗辑思维、得到营销外脑，小米生态链企业顾问。曾任暴风魔镜创意合伙人，第九课堂联合创始人，暴风影音高级市场总监，奥美、蓝色光标等。2007年获得戛纳广告节综合传播项铜狮奖，2016年虎嗅网最佳作者。', '["人气导师","营销达人","资深导师","品牌专家"]', '2024-12-28 06:59:16.218299', '2025-02-17 02:32:22.861', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737437846904-CCYH2327_w1207.JPG', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737437598139-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (4, '张培', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737365949591-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120173841.jpg', 8000, '北京钇财咨询服务有限公司创始人', '["资深导师","创业导师"]', '2024-12-28 06:59:16.218299', '2025-02-17 02:32:42.939', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737365975090-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120173841.jpg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737365981135-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (2, '王博轩', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737366117517-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120174138.jpg', 20000, 'NewMoney撕董会主理人、10年媒体人、TOP10财经博主、企业家商业IP领航员', '["资深导师","创业导师"]', '2024-12-28 06:59:16.218299', '2025-02-17 02:33:20.839', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737368583888-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120182232.jpg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737366575170-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (3, '臧其超', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737447343020-t048ecbfcd299e3c1fb.jpg', 20000, '中国咨询式股权投融资专家，深圳三藏资本董事长、著作有《销售团队这样带》《人人都是销售高手》《老板的格局》等。', '["营销达人","资深导师","投资专家"]', '2024-12-28 06:59:16.218299', '2025-02-17 03:02:00.099', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737361524488-40b188342284ad2adc904c7310e6c0e8~tplv-dy-resize-origshort-autoq-75_330.jpeg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737355100833-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (23, '温伟强', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743760324823-20250320-00077.JPG', 1000, '在广州中医药大学《黄帝内经》教研室工作。从事教学、科研、临床医疗工作四十多年，曾获广东省教学成果省级一等奖，国家级教学成果二等奖等。在校主要讲授“黄帝内经"、“中医养生学”、“中医药膳学"，“中医环保学”，“国学与中医”等课程。从事开展中医“治未病"研究，中医药食疗研发。曾多次在省、市电视台讲授中医养生食疗。善长应用《黄帝内经》的理论，灵活运用《黄帝内经》的尺肤诊断方法，采用独特的古方于临床治疗，善长治疗呼吸系统，消化系统，肿瘤，老年病等疾病。
', '[]', '2025-04-04 09:48:48.43', '2025-04-04 09:52:16.794', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743759430631-%E6%B8%A9%E4%BC%9F%E5%BC%BA.png', NULL);
INSERT INTO "public"."asm_teacher" VALUES (22, '徐小敏', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743410596659-20250320-00074.JPG', 1000, '师承刘金才(中国书画家协会主席，齐白石关门弟子)，陈璧初(中国书画家协会副主席，世界艺术百年传世十大名家)，陈大络(台湾省师范大学大学教授，台湾著名书法家)，多年研究书法篆刻.古汉字教育。
个人成就：
（1）2008年北京奥运作品《龙之魂·夏之梦》，被选送参加“奥林匹克之旅—中华民族艺术珍品文化节”，并被国家博物馆永久收藏。
（2）2011年深圳大运作品《从这里开始》参加全国名人书画联展荣获一等奖。
（3）2011年7月1日，2012年7月1日中共中国书画家协会委员会举办全国名人书画联展，其书法、篆刻作品荣获一等奖。
（4）2012年元旦，其创作的书法“龙”字被中国书画家协会授予“当代名家”荣誉称号。
（5）2020年被中国国际报告文学研究会聘为国学文化传承委员会专家委员。
（6）2021年荣获“2021纂刻行业杰出贡献人物”。
（7）2021年担任CCTV决策中国栏目副理事长。
（8）2022年作为“行业影响力领军人物”被中国名人焦点网-名人名家百科收录。
（9）2022年评为名人名家百科“首席篆刻家”兼“终身荣誉顾问”
（10）2023年荣获“中国纂刻艺术非遗传承导师”荣誉称号', '["资深导师"]', '2025-03-31 08:44:32.036', '2025-04-04 09:49:06.045', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743747036686-%E5%BE%90%E5%B0%8F%E6%95%8F.png', NULL);
INSERT INTO "public"."asm_teacher" VALUES (19, '张华', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744692056-全员AI.png', 2000, 'AI 培训导师,薪炬科技·首席培训导师|AIGC提示工程培训导师|企业教练,15年教育培训课程研发和培训经验,工信部教育与考试中心·AIGC提示工程培训导师,中央民族大学特聘程序设计讲师', '["\u6d4b\u8bd5"]', '2025-03-10 08:33:56.747', '2025-04-15 12:28:02.381345', NULL, NULL);

-- ----------------------------
-- Table structure for asm_user_usages
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_user_usages";
CREATE TABLE "public"."asm_user_usages" (
  "id" int4 NOT NULL DEFAULT nextval('asm_user_usages_id_seq'::regclass),
  "user_id" int4,
  "resource_type" "public"."resource_type_enum",
  "resource_id" varchar COLLATE "pg_catalog"."default",
  "action_type" "public"."action_type_enum",
  "duration" int4,
  "session_id" varchar COLLATE "pg_catalog"."default",
  "tokens_used" int4,
  "progress" float8,
  "meta_data" jsonb,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "ip_address" varchar COLLATE "pg_catalog"."default",
  "device_info" varchar COLLATE "pg_catalog"."default",
  "total_price" float8,
  "currency" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."asm_user_usages"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_user_usages"."resource_type" IS '资源类型';
COMMENT ON COLUMN "public"."asm_user_usages"."resource_id" IS '资源ID';
COMMENT ON COLUMN "public"."asm_user_usages"."action_type" IS '动作类型';
COMMENT ON COLUMN "public"."asm_user_usages"."duration" IS '使用时长(秒)';
COMMENT ON COLUMN "public"."asm_user_usages"."session_id" IS '会话ID';
COMMENT ON COLUMN "public"."asm_user_usages"."tokens_used" IS '使用的令牌数';
COMMENT ON COLUMN "public"."asm_user_usages"."progress" IS '进度百分比';
COMMENT ON COLUMN "public"."asm_user_usages"."meta_data" IS '元数据';
COMMENT ON COLUMN "public"."asm_user_usages"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_user_usages"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_user_usages"."ip_address" IS 'IP地址';
COMMENT ON COLUMN "public"."asm_user_usages"."device_info" IS '设备信息';
COMMENT ON COLUMN "public"."asm_user_usages"."total_price" IS '总价格';
COMMENT ON COLUMN "public"."asm_user_usages"."currency" IS '货币';
COMMENT ON TABLE "public"."asm_user_usages" IS '用户用量表';

-- ----------------------------
-- Records of asm_user_usages
-- ----------------------------
INSERT INTO "public"."asm_user_usages" VALUES (47, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 2297, NULL, '{"latency": 2.786367211956531, "currency": "USD", "total_price": "0.028385", "prompt_price": "0.02630", "total_tokens": 2297, "prompt_tokens": 5260, "completion_price": "0.002085", "completion_tokens": 139, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:06:21.105407', '2025-04-14 18:06:21.105436', NULL, NULL, 0.596085, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (48, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 3162, NULL, '{"latency": 2.1191015518270433, "currency": "USD", "total_price": "0.036525", "prompt_price": "0.034950", "total_tokens": 3162, "prompt_tokens": 6990, "completion_price": "0.001575", "completion_tokens": 105, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:06:39.964289', '2025-04-14 18:06:39.96436', NULL, NULL, 0.767025, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (49, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 3993, NULL, '{"latency": 2.0883974358439445, "currency": "USD", "total_price": "0.044760", "prompt_price": "0.04326", "total_tokens": 3993, "prompt_tokens": 8652, "completion_price": "0.001500", "completion_tokens": 100, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:06:56.895717', '2025-04-14 18:06:56.895769', NULL, NULL, 0.9399599999999999, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (50, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 4819, NULL, '{"latency": 2.6028442718088627, "currency": "USD", "total_price": "0.052975", "prompt_price": "0.05152", "total_tokens": 4819, "prompt_tokens": 10304, "completion_price": "0.001455", "completion_tokens": 97, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:07:16.437129', '2025-04-14 18:07:16.437205', NULL, NULL, 1.112475, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (51, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 5642, NULL, '{"latency": 2.728513046167791, "currency": "USD", "total_price": "0.06113", "prompt_price": "0.059750", "total_tokens": 5642, "prompt_tokens": 11950, "completion_price": "0.001380", "completion_tokens": 92, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:09:52.415905', '2025-04-14 18:09:52.415952', NULL, NULL, 1.2837299999999998, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (52, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 6460, NULL, '{"latency": 2.118840980809182, "currency": "USD", "total_price": "0.069385", "prompt_price": "0.067930", "total_tokens": 6460, "prompt_tokens": 13586, "completion_price": "0.001455", "completion_tokens": 97, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:10:05.802208', '2025-04-14 18:10:05.802302', NULL, NULL, 1.457085, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (53, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 7283, NULL, '{"latency": 3.316557190846652, "currency": "USD", "total_price": "0.077660", "prompt_price": "0.07616", "total_tokens": 7283, "prompt_tokens": 15232, "completion_price": "0.001500", "completion_tokens": 100, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:13:30.672316', '2025-04-14 18:13:30.67236', NULL, NULL, 1.63086, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (54, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 8109, NULL, '{"latency": 2.6237702989019454, "currency": "USD", "total_price": "0.085875", "prompt_price": "0.08442", "total_tokens": 8109, "prompt_tokens": 16884, "completion_price": "0.001455", "completion_tokens": 97, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:13:46.102414', '2025-04-14 18:13:46.102482', NULL, NULL, 1.803375, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (55, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 8932, NULL, '{"latency": 2.316791818011552, "currency": "USD", "total_price": "0.09600", "prompt_price": "0.093000", "total_tokens": 8932, "prompt_tokens": 18600, "completion_price": "0.003000", "completion_tokens": 200, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:21:44.58517', '2025-04-14 18:21:44.585221', NULL, NULL, 2.016, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (56, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 9915, NULL, '{"latency": 3.726080601103604, "currency": "USD", "total_price": "0.049965", "prompt_price": "0.04938", "total_tokens": 9915, "prompt_tokens": 9876, "completion_price": "0.000585", "completion_tokens": 39, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:22:15.988799', '2025-04-14 18:22:15.988883', NULL, NULL, 1.0492650000000001, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (57, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 10025, NULL, '{"latency": 1.8115398017689586, "currency": "USD", "total_price": "0.102255", "prompt_price": "0.100095", "total_tokens": 10025, "prompt_tokens": 20019, "completion_price": "0.002160", "completion_tokens": 144, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:23:00.452504', '2025-04-14 18:23:00.452546', NULL, NULL, 2.147355, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (58, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 10197, NULL, '{"latency": 2.5770684923045337, "currency": "USD", "total_price": "0.105735", "prompt_price": "0.104130", "total_tokens": 10197, "prompt_tokens": 20826, "completion_price": "0.001605", "completion_tokens": 107, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:23:11.314611', '2025-04-14 18:23:11.314666', NULL, NULL, 2.2204349999999997, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (59, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 10795, NULL, '{"latency": 3.0939393220469356, "currency": "USD", "total_price": "0.112920", "prompt_price": "0.110385", "total_tokens": 10795, "prompt_tokens": 22077, "completion_price": "0.002535", "completion_tokens": 169, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:30:42.586589', '2025-04-14 18:30:42.586638', NULL, NULL, 2.37132, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (60, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 11613, NULL, '{"latency": 2.628657122142613, "currency": "USD", "total_price": "0.118400", "prompt_price": "0.115865", "total_tokens": 11613, "prompt_tokens": 23173, "completion_price": "0.002535", "completion_tokens": 169, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:33:28.750229', '2025-04-14 18:33:28.750302', NULL, NULL, 2.4863999999999997, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (61, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 11791, NULL, '{"latency": 1.7106898841448128, "currency": "USD", "total_price": "0.121735", "prompt_price": "0.12007", "total_tokens": 11791, "prompt_tokens": 24014, "completion_price": "0.001665", "completion_tokens": 111, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:33:46.651427', '2025-04-14 18:33:46.65148', NULL, NULL, 2.5564349999999996, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (62, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 12396, NULL, '{"latency": 1.9695790619589388, "currency": "USD", "total_price": "0.12774", "prompt_price": "0.126120", "total_tokens": 12396, "prompt_tokens": 25224, "completion_price": "0.001620", "completion_tokens": 108, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:34:31.238744', '2025-04-14 18:34:31.238772', NULL, NULL, 2.68254, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (63, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 12998, NULL, '{"latency": 1.9327362379990518, "currency": "USD", "total_price": "0.135035", "prompt_price": "0.132425", "total_tokens": 12998, "prompt_tokens": 26485, "completion_price": "0.002610", "completion_tokens": 174, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:37:07.789701', '2025-04-14 18:37:07.789728', NULL, NULL, 2.8357349999999997, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (64, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 14013, NULL, '{"latency": 2.801678287796676, "currency": "USD", "total_price": "0.143260", "prompt_price": "0.139375", "total_tokens": 14013, "prompt_tokens": 27875, "completion_price": "0.003885", "completion_tokens": 259, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:46:33.642921', '2025-04-14 18:46:33.642986', NULL, NULL, 3.0084600000000004, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (65, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 14179, NULL, '{"latency": 2.239949475042522, "currency": "USD", "total_price": "0.145300", "prompt_price": "0.14395", "total_tokens": 14179, "prompt_tokens": 28790, "completion_price": "0.001350", "completion_tokens": 90, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:48:43.341282', '2025-04-14 18:48:43.341313', NULL, NULL, 3.0513000000000003, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (66, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 14759, NULL, '{"latency": 5.434377308003604, "currency": "USD", "total_price": "0.151160", "prompt_price": "0.14975", "total_tokens": 14759, "prompt_tokens": 29950, "completion_price": "0.001410", "completion_tokens": 94, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:49:18.878738', '2025-04-14 18:49:18.878779', NULL, NULL, 3.17436, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (67, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 15343, NULL, '{"latency": 1.7641127351671457, "currency": "USD", "total_price": "0.158450", "prompt_price": "0.155885", "total_tokens": 15343, "prompt_tokens": 31177, "completion_price": "0.002565", "completion_tokens": 171, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:51:21.713637', '2025-04-14 18:51:21.713685', NULL, NULL, 3.3274500000000002, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (68, 38, 'AGENT', '42', 'CHAT', NULL, '4673e058-7511-400e-8e93-26bbe85d18ff', 16160, NULL, '{"latency": 4.124046594835818, "currency": "USD", "total_price": "0.16375", "prompt_price": "0.16132", "total_tokens": 16160, "prompt_tokens": 32264, "completion_price": "0.00243", "completion_tokens": 162, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-14 18:56:04.528912', '2025-04-14 18:56:04.528944', NULL, NULL, 3.4387499999999998, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (69, 1, 'AGENT', '20', 'CHAT', NULL, 'd002d043-cec6-49c0-b75c-258c4cf3fb25', 3685, NULL, '{"latency": 22.48126213811338, "currency": "USD", "total_price": "0.025227", "prompt_price": "0.007512", "total_tokens": 3685, "prompt_tokens": 2504, "completion_price": "0.017715", "completion_tokens": 1181, "prompt_price_unit": "0.000001", "prompt_unit_price": "3", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-23 12:34:10.521653', '2025-04-23 12:34:10.521663', NULL, NULL, 0.529767, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (70, 1, 'AGENT', '42', 'CHAT', NULL, 'b05026f6-1a60-4231-8559-fffbcc793b69', 1337, NULL, '{"latency": 2.113620355259627, "currency": "USD", "total_price": "0.006775", "prompt_price": "0.00664", "total_tokens": 1337, "prompt_tokens": 1328, "completion_price": "0.000135", "completion_tokens": 9, "prompt_price_unit": "0.000001", "prompt_unit_price": "5", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-23 16:49:37.882765', '2025-04-23 16:49:37.8828', NULL, NULL, 0.14227499999999998, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (71, 1, 'AGENT', '20', 'CHAT', NULL, '691bf408-64ac-409c-bf97-5ef898d1490c', 2279, NULL, '{"latency": 15.99338220898062, "currency": "USD", "total_price": "0.016437", "prompt_price": "0.004437", "total_tokens": 2279, "prompt_tokens": 1479, "completion_price": "0.012", "completion_tokens": 800, "prompt_price_unit": "0.000001", "prompt_unit_price": "3", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-24 15:20:47.835073', '2025-04-24 15:20:47.835089', NULL, NULL, 0.34517699999999996, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (72, 1, 'AGENT', '20', 'CHAT', NULL, '691bf408-64ac-409c-bf97-5ef898d1490c', 3283, NULL, '{"latency": 19.005155669059604, "currency": "USD", "total_price": "0.021837", "prompt_price": "0.006852", "total_tokens": 3283, "prompt_tokens": 2284, "completion_price": "0.014985", "completion_tokens": 999, "prompt_price_unit": "0.000001", "prompt_unit_price": "3", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-24 15:32:41.632064', '2025-04-24 15:32:41.632073', NULL, NULL, 0.458577, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (73, 1, 'AGENT', '1', 'CHAT', NULL, '218f4450-c0f9-48b8-b551-c211e178812f', 70, NULL, '{"latency": 15.357380228117108, "currency": "USD", "total_price": "0", "prompt_price": "0", "total_tokens": 70, "prompt_tokens": 35, "completion_price": "0", "completion_tokens": 35, "prompt_price_unit": "0", "prompt_unit_price": "0", "completion_price_unit": "0", "completion_unit_price": "0"}', '2025-04-24 19:27:50.363661', '2025-04-24 19:27:50.363671', NULL, NULL, 0, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (74, 1, 'AGENT', '1', 'CHAT', NULL, '218f4450-c0f9-48b8-b551-c211e178812f', 240, NULL, '{"latency": 23.602841209154576, "currency": "USD", "total_price": "0", "prompt_price": "0", "total_tokens": 240, "prompt_tokens": 120, "completion_price": "0", "completion_tokens": 120, "prompt_price_unit": "0", "prompt_unit_price": "0", "completion_price_unit": "0", "completion_unit_price": "0"}', '2025-04-24 19:35:11.500446', '2025-04-24 19:35:11.500455', NULL, NULL, 0, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (75, 1, 'AGENT', '1', 'CHAT', NULL, '218f4450-c0f9-48b8-b551-c211e178812f', 622, NULL, '{"latency": 47.25703358510509, "currency": "USD", "total_price": "0", "prompt_price": "0", "total_tokens": 622, "prompt_tokens": 311, "completion_price": "0", "completion_tokens": 311, "prompt_price_unit": "0", "prompt_unit_price": "0", "completion_price_unit": "0", "completion_unit_price": "0"}', '2025-04-24 19:39:40.641091', '2025-04-24 19:39:40.64111', NULL, NULL, 0, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (76, 1, 'AGENT', '1', 'CHAT', NULL, '218f4450-c0f9-48b8-b551-c211e178812f', 2006, NULL, '{"latency": 26.519886317197233, "currency": "USD", "total_price": "0", "prompt_price": "0", "total_tokens": 2006, "prompt_tokens": 1003, "completion_price": "0", "completion_tokens": 1003, "prompt_price_unit": "0", "prompt_unit_price": "0", "completion_price_unit": "0", "completion_unit_price": "0"}', '2025-04-24 19:51:54.829036', '2025-04-24 19:51:54.829048', NULL, NULL, 0, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (77, 1, 'AGENT', '1', 'CHAT', NULL, '218f4450-c0f9-48b8-b551-c211e178812f', 2664, NULL, '{"latency": 61.44055429333821, "currency": "USD", "total_price": "0", "prompt_price": "0", "total_tokens": 2664, "prompt_tokens": 1332, "completion_price": "0", "completion_tokens": 1332, "prompt_price_unit": "0", "prompt_unit_price": "0", "completion_price_unit": "0", "completion_unit_price": "0"}', '2025-04-24 19:59:16.644837', '2025-04-24 19:59:16.644857', NULL, NULL, 0, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (78, 1, 'AGENT', '20', 'CHAT', NULL, '691bf408-64ac-409c-bf97-5ef898d1490c', 4322, NULL, '{"latency": 21.10689793806523, "currency": "USD", "total_price": "0.025374", "prompt_price": "0.009864", "total_tokens": 4322, "prompt_tokens": 3288, "completion_price": "0.01551", "completion_tokens": 1034, "prompt_price_unit": "0.000001", "prompt_unit_price": "3", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-24 20:16:57.400717', '2025-04-24 20:16:57.400736', NULL, NULL, 0.5328539999999999, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (79, 1, 'AGENT', '1', 'CHAT', NULL, '218f4450-c0f9-48b8-b551-c211e178812f', 4308, NULL, '{"latency": 46.687313084024936, "currency": "USD", "total_price": "0", "prompt_price": "0", "total_tokens": 4308, "prompt_tokens": 2154, "completion_price": "0", "completion_tokens": 2154, "prompt_price_unit": "0", "prompt_unit_price": "0", "completion_price_unit": "0", "completion_unit_price": "0"}', '2025-04-25 01:24:05.075724', '2025-04-25 01:24:05.075738', NULL, NULL, 0, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (80, 1, 'AGENT', '21', 'CHAT', NULL, '883c917d-4dfb-4e7a-b83c-6eb6015daf5e', 790, NULL, '{"latency": 3.777491909917444, "currency": "USD", "total_price": "0.002658", "prompt_price": "0.002298", "total_tokens": 790, "prompt_tokens": 766, "completion_price": "0.00036", "completion_tokens": 24, "prompt_price_unit": "0.000001", "prompt_unit_price": "3", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-25 22:58:25.152464', '2025-04-25 22:58:25.152476', NULL, NULL, 0.055818000000000006, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (81, 38, 'AGENT', '20', 'CHAT', NULL, 'ae27ed04-b596-4e1a-9dc6-3d1964c9d447', 0, NULL, '{"latency": 4.5160504300147295, "currency": "USD", "total_price": "0E-7", "prompt_price": "0E-7", "total_tokens": 0, "prompt_tokens": 0, "completion_price": "0E-7", "completion_tokens": 0, "prompt_price_unit": "0.000001", "prompt_unit_price": "3", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-27 10:09:09.981232', '2025-04-27 10:09:09.981243', NULL, NULL, 0, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (82, 38, 'AGENT', '22', 'CHAT', NULL, '5bddde30-56c9-4d7e-8dbd-6be2d7375f0e', 3919, NULL, '{"latency": 5.209735077340156, "currency": "USD", "total_price": "0.014769", "prompt_price": "0.011004", "total_tokens": 3919, "prompt_tokens": 3668, "completion_price": "0.003765", "completion_tokens": 251, "prompt_price_unit": "0.000001", "prompt_unit_price": "3", "completion_price_unit": "0.000001", "completion_unit_price": "15"}', '2025-04-27 10:09:25.743844', '2025-04-27 10:09:25.743854', NULL, NULL, 0.31014899999999995, 'CNY');
INSERT INTO "public"."asm_user_usages" VALUES (83, 1, 'AGENT', '1', 'CHAT', NULL, '50227917-ad6a-4da1-97fc-447f73e547ce', 8156, NULL, '{"latency": 54.474560282193124, "currency": "USD", "total_price": "0", "prompt_price": "0", "total_tokens": 8156, "prompt_tokens": 4078, "completion_price": "0", "completion_tokens": 4078, "prompt_price_unit": "0", "prompt_unit_price": "0", "completion_price_unit": "0", "completion_unit_price": "0"}', '2025-05-07 14:35:32.729367', '2025-05-07 14:35:32.729376', NULL, NULL, 0, 'CNY');

-- ----------------------------
-- Table structure for asm_users
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_users";
CREATE TABLE "public"."asm_users" (
  "id" int4 NOT NULL DEFAULT nextval('asm_users_id_seq'::regclass),
  "username" varchar(50) COLLATE "pg_catalog"."default",
  "nickname" varchar(50) COLLATE "pg_catalog"."default",
  "password" varchar(100) COLLATE "pg_catalog"."default",
  "points" int4,
  "user_type" "public"."user_type_enum",
  "phone" varchar(20) COLLATE "pg_catalog"."default",
  "avatar_url" varchar COLLATE "pg_catalog"."default",
  "openid" varchar(50) COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "birthday" date,
  "address" varchar COLLATE "pg_catalog"."default",
  "gender" "public"."gender_enum",
  "membership_expires_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_users"."username" IS '用户名';
COMMENT ON COLUMN "public"."asm_users"."nickname" IS '用户昵称';
COMMENT ON COLUMN "public"."asm_users"."password" IS '密码(加密存储)';
COMMENT ON COLUMN "public"."asm_users"."points" IS '积分';
COMMENT ON COLUMN "public"."asm_users"."user_type" IS '用户类型';
COMMENT ON COLUMN "public"."asm_users"."phone" IS '手机号';
COMMENT ON COLUMN "public"."asm_users"."avatar_url" IS '头像URL';
COMMENT ON COLUMN "public"."asm_users"."openid" IS '微信OpenID';
COMMENT ON COLUMN "public"."asm_users"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_users"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_users"."birthday" IS '出生日期';
COMMENT ON COLUMN "public"."asm_users"."address" IS '地址';
COMMENT ON COLUMN "public"."asm_users"."gender" IS '性别';
COMMENT ON COLUMN "public"."asm_users"."membership_expires_at" IS '会员到期时间';
COMMENT ON TABLE "public"."asm_users" IS '用户信息表';

-- ----------------------------
-- Records of asm_users
-- ----------------------------
INSERT INTO "public"."asm_users" VALUES (38, '17729916575', '用户6575', NULL, 0, 'LOCAL', '17729916575', 'https://i.pravatar.cc/300', 'olDhg7NVwpXzoMlvCgL8ojCUvzD0', '2025-02-20 21:58:34.236378', '2025-03-20 14:43:55.249646', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (1, '12345678910', '用户8910', NULL, 0, 'LOCAL', '13082523215', 'https://i.pravatar.cc/300', 'olDhg7NVwpXzoMlvCgL8ojCUvzD0', '2025-03-22 21:05:32.453258', '2025-03-23 17:48:27.970523', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_activities_id_seq"
OWNED BY "public"."asm_activities"."id";
SELECT setval('"public"."asm_activities_id_seq"', 2, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_activity_reward_records_id_seq"
OWNED BY "public"."asm_activity_reward_records"."id";
SELECT setval('"public"."asm_activity_reward_records_id_seq"', 86, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_agent_starters_id_seq"
OWNED BY "public"."asm_agent_starters"."id";
SELECT setval('"public"."asm_agent_starters_id_seq"', 38, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_agents_id_seq"
OWNED BY "public"."asm_agents"."id";
SELECT setval('"public"."asm_agents_id_seq"', 66, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_coupons_id_seq"
OWNED BY "public"."asm_coupons"."id";
SELECT setval('"public"."asm_coupons_id_seq"', 97, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_course_id_seq"
OWNED BY "public"."asm_course"."id";
SELECT setval('"public"."asm_course_id_seq"', 73, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_course_section_id_seq"
OWNED BY "public"."asm_course_section"."id";
SELECT setval('"public"."asm_course_section_id_seq"', 63, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_investment_show_application_id_seq"
OWNED BY "public"."asm_investment_show_application"."id";
SELECT setval('"public"."asm_investment_show_application_id_seq"', 12, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_investment_show_id_seq"
OWNED BY "public"."asm_investment_show"."id";
SELECT setval('"public"."asm_investment_show_id_seq"', 6, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_orders_id_seq"
OWNED BY "public"."asm_orders"."id";
SELECT setval('"public"."asm_orders_id_seq"', 6, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_system_parameters_id_seq"
OWNED BY "public"."asm_system_parameters"."id";
SELECT setval('"public"."asm_system_parameters_id_seq"', 5, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_teacher_id_seq"
OWNED BY "public"."asm_teacher"."id";
SELECT setval('"public"."asm_teacher_id_seq"', 25, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."asm_user_accounts_id_seq"', 3, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."asm_user_usages_id_seq"', 83, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_users_id_seq"
OWNED BY "public"."asm_users"."id";
SELECT setval('"public"."asm_users_id_seq"', 211, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."qu_user_assets_id_seq"', 1, false);

-- ----------------------------
-- Primary Key structure for table alembic_version
-- ----------------------------
ALTER TABLE "public"."alembic_version" ADD CONSTRAINT "alembic_version_pkc" PRIMARY KEY ("version_num");

-- ----------------------------
-- Indexes structure for table asm_activities
-- ----------------------------
CREATE INDEX "ix_asm_activities_title" ON "public"."asm_activities" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_activities
-- ----------------------------
ALTER TABLE "public"."asm_activities" ADD CONSTRAINT "asm_activities_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_activity_reward_records
-- ----------------------------
CREATE INDEX "ix_asm_activity_reward_records_user_id" ON "public"."asm_activity_reward_records" USING btree (
  "user_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_activity_reward_records
-- ----------------------------
ALTER TABLE "public"."asm_activity_reward_records" ADD CONSTRAINT "asm_activity_reward_records_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table asm_admins
-- ----------------------------
ALTER TABLE "public"."asm_admins" ADD CONSTRAINT "asm_admins_username_key" UNIQUE ("username");

-- ----------------------------
-- Primary Key structure for table asm_admins
-- ----------------------------
ALTER TABLE "public"."asm_admins" ADD CONSTRAINT "asm_users_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_agent_starters
-- ----------------------------
CREATE INDEX "ix_asm_agent_starters_question" ON "public"."asm_agent_starters" USING btree (
  "question" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_agent_starters
-- ----------------------------
ALTER TABLE "public"."asm_agent_starters" ADD CONSTRAINT "asm_agent_starters_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_agents
-- ----------------------------
CREATE INDEX "ix_asm_agents_name" ON "public"."asm_agents" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_agents
-- ----------------------------
ALTER TABLE "public"."asm_agents" ADD CONSTRAINT "asm_agents_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table asm_coupons
-- ----------------------------
ALTER TABLE "public"."asm_coupons" ADD CONSTRAINT "asm_coupons_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_course
-- ----------------------------
CREATE INDEX "ix_asm_course_title" ON "public"."asm_course" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_course
-- ----------------------------
ALTER TABLE "public"."asm_course" ADD CONSTRAINT "asm_course_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_course_section
-- ----------------------------
CREATE INDEX "ix_asm_course_section_title" ON "public"."asm_course_section" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_course_section
-- ----------------------------
ALTER TABLE "public"."asm_course_section" ADD CONSTRAINT "asm_course_section_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_investment_show
-- ----------------------------
CREATE INDEX "ix_asm_investment_show_episode" ON "public"."asm_investment_show" USING btree (
  "episode" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "ix_asm_investment_show_title" ON "public"."asm_investment_show" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_investment_show
-- ----------------------------
ALTER TABLE "public"."asm_investment_show" ADD CONSTRAINT "asm_investment_show_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_investment_show_application
-- ----------------------------
CREATE INDEX "ix_asm_investment_show_application_company_name" ON "public"."asm_investment_show_application" USING btree (
  "company_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_investment_show_application
-- ----------------------------
ALTER TABLE "public"."asm_investment_show_application" ADD CONSTRAINT "asm_investment_show_application_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_orders
-- ----------------------------
CREATE UNIQUE INDEX "ix_asm_orders_order_no" ON "public"."asm_orders" USING btree (
  "order_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_orders
-- ----------------------------
ALTER TABLE "public"."asm_orders" ADD CONSTRAINT "asm_orders_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_system_parameters
-- ----------------------------
CREATE UNIQUE INDEX "ix_asm_system_parameters_key" ON "public"."asm_system_parameters" USING btree (
  "key" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_system_parameters
-- ----------------------------
ALTER TABLE "public"."asm_system_parameters" ADD CONSTRAINT "asm_system_parameters_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_teacher
-- ----------------------------
CREATE INDEX "ix_asm_teacher_name" ON "public"."asm_teacher" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_teacher
-- ----------------------------
ALTER TABLE "public"."asm_teacher" ADD CONSTRAINT "asm_teacher_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_user_usages
-- ----------------------------
CREATE INDEX "ix_qu_user_usages_resource_id" ON "public"."asm_user_usages" USING btree (
  "resource_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "ix_qu_user_usages_user_id" ON "public"."asm_user_usages" USING btree (
  "user_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_user_usages
-- ----------------------------
ALTER TABLE "public"."asm_user_usages" ADD CONSTRAINT "qu_user_usages_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table asm_users
-- ----------------------------
ALTER TABLE "public"."asm_users" ADD CONSTRAINT "asm_users_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table asm_agent_starters
-- ----------------------------
ALTER TABLE "public"."asm_agent_starters" ADD CONSTRAINT "asm_agent_starters_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."asm_agents" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_coupons
-- ----------------------------
ALTER TABLE "public"."asm_coupons" ADD CONSTRAINT "asm_coupons_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_course
-- ----------------------------
ALTER TABLE "public"."asm_course" ADD CONSTRAINT "asm_course_teacher_id_fkey" FOREIGN KEY ("teacher_id") REFERENCES "public"."asm_teacher" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_course_section
-- ----------------------------
ALTER TABLE "public"."asm_course_section" ADD CONSTRAINT "asm_course_section_course_id_fkey" FOREIGN KEY ("course_id") REFERENCES "public"."asm_course" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_investment_show_application
-- ----------------------------
ALTER TABLE "public"."asm_investment_show_application" ADD CONSTRAINT "asm_investment_show_application_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_orders
-- ----------------------------
ALTER TABLE "public"."asm_orders" ADD CONSTRAINT "asm_orders_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."asm_agents" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."asm_orders" ADD CONSTRAINT "asm_orders_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "public"."asm_coupons" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."asm_orders" ADD CONSTRAINT "asm_orders_course_id_fkey" FOREIGN KEY ("course_id") REFERENCES "public"."asm_course" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."asm_orders" ADD CONSTRAINT "asm_orders_teacher_id_fkey" FOREIGN KEY ("teacher_id") REFERENCES "public"."asm_teacher" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."asm_orders" ADD CONSTRAINT "asm_orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_user_usages
-- ----------------------------
ALTER TABLE "public"."asm_user_usages" ADD CONSTRAINT "asm_user_usages" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
