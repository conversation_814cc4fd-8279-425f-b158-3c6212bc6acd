import { AgentType } from "./schemas";

export const entityKey = "agent";
export const modeMap: Record<string, { label: string, color: string }> = {
    [AgentType.CHAT]: { label: '对话聊天', color: 'green' },
    [AgentType.AGENT_CHAT]: { label: '智能体聊天', color: 'purple' },
    [AgentType.WORKFLOW]: { label: '工作流', color: 'orange' },
    [AgentType.COMPLETION]: { label: '文本补全', color: 'blue' },
};

export const modeOptions = [
    { label: '对话聊天', value: AgentType.CHAT },
    { label: '智能体聊天', value: AgentType.AGENT_CHAT },
    { label: '工作流', value: AgentType.WORKFLOW },
    { label: '文本补全', value: AgentType.COMPLETION },
];