import { Show } from "@refinedev/antd";
import { Typography, Card, Tag, Space, Descriptions } from "antd";
import React from "react";
import { Agent } from "../schemas";
import { entityKey } from "../consts";
import { useShow } from "@refinedev/core";
const { Title } = Typography;

const AgentShow: React.FC = () => {
  const { query } = useShow<Agent>({
    resource: entityKey
  });
  const { data, isLoading } = query;
  const record = data?.data;

  const renderAgentType = (type?: string) => {
    if (!type) return null;
    
    const modeMap: Record<string, {label: string, color: string}> = {
      'CHAT': { label: '对话聊天', color: 'green' },
      'AGENT_CHAT': { label: '智能体聊天', color: 'purple' },
      'WORKFLOW': { label: '工作流', color: 'orange' },
      'COMPLETION': { label: '文本补全', color: 'blue' },
    };
    
    return <Tag color={modeMap[type]?.color}>{modeMap[type]?.label || type}</Tag>;
  };

  return (
    <Show isLoading={isLoading}>
      {record && (
        <Card>
          <Title level={4}>{record.name}</Title>
          
          <Descriptions bordered column={2}>
            <Descriptions.Item label="应用ID">
              {record.id}
            </Descriptions.Item>
            
            <Descriptions.Item label="应用模式">
              {renderAgentType(record.type)}
            </Descriptions.Item>
            
            <Descriptions.Item label="描述">
              {record.description || '-'}
            </Descriptions.Item>
            
            <Descriptions.Item label="API密钥">
              {record.api_key ? `${record.api_key.substring(0, 8)}...` : '-'}
            </Descriptions.Item>
            
            <Descriptions.Item label="月付价格">
              ¥{record.monthly_price?.toFixed(2)}
            </Descriptions.Item>
            
            <Descriptions.Item label="原月付价格">
              {record.original_monthly_price ? `¥${record.original_monthly_price.toFixed(2)}` : '-'}
            </Descriptions.Item>
            
            <Descriptions.Item label="年付价格">
              ¥{record.yearly_price?.toFixed(2)}
            </Descriptions.Item>
            
            <Descriptions.Item label="原年付价格">
              {record.original_yearly_price ? `¥${record.original_yearly_price.toFixed(2)}` : '-'}
            </Descriptions.Item>
            
            <Descriptions.Item label="标签" span={2}>
              {record.tags && record.tags.length > 0 ? (
                <Space>
                  {record.tags.map((tag, index) => (
                    <Tag key={index} color="cyan">{tag}</Tag>
                  ))}
                </Space>
              ) : '-'}
            </Descriptions.Item>
            
            {record.icon_url && (
              <Descriptions.Item label="图标" span={2}>
                <img 
                  src={record.icon_url} 
                  alt={record.name}
                  style={{ 
                    maxWidth: '100px',
                    maxHeight: '100px',
                    objectFit: 'contain'
                  }}
                />
              </Descriptions.Item>
            )}
            
            <Descriptions.Item label="创建时间">
              {record.created_at && new Date(record.created_at).toLocaleString()}
            </Descriptions.Item>
            
            <Descriptions.Item label="更新时间">
              {record.updated_at && new Date(record.updated_at).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      )}
    </Show>
  );
}

export default AgentShow; 