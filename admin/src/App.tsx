import { Authenticated, Refine } from "@refinedev/core";
import { DevtoolsPanel, DevtoolsProvider } from "@refinedev/devtools";
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";

import {
  ErrorComponent,
  ThemedLayoutV2,
  ThemedSiderV2,
  useNotificationProvider,
} from "@refinedev/antd";
import "@refinedev/antd/dist/reset.css";

import { AdminCreate, AdminEdit, AdminList, AdminShow } from "@/admin/pages";
import { Login } from "@/admin/pages/Login";
import { authProvider } from "@/admin/providers";
import { AgentCreate, AgentEdit, AgentList, AgentShow } from "@/agent";
import { CourseCreate, CourseEdit, CourseList, CourseShow } from "@/course";
import { Header } from "@/common/components";
import { ColorModeContextProvider } from "@/common/contexts";
import { customDataProvider } from "@/common/dataProvider";
import { OrderEdit, OrderList, OrderShow } from "@/order";
import {
  PaymentPlanCreate,
  PaymentPlanEdit,
  PaymentPlanList,
  PaymentPlanShow,
} from "@/paymentPlan";

import { UserCreate, UserEdit, UserList, UserShow } from "@/user";

import routerBindings, {
  CatchAllNavigate,
  DocumentTitleHandler,
  NavigateToResource,
  UnsavedChangesNotifier,
} from "@refinedev/react-router";
import { App as AntdApp } from "antd";
import {
  FaBoxOpen,
  FaGem,
  FaGraduationCap,
  FaMoneyBillWave,
  FaRobot,
  FaUser,
  FaUserShield,
  FaWallet,
  FaExchangeAlt,
  FaFileInvoiceDollar,
} from "react-icons/fa";
import { IoLogoOctocat } from "react-icons/io";
import { HashRouter, Outlet, Route, Routes } from "react-router";
import { APP_TITLE } from "./common";

function App() {
  return (
    <HashRouter>
      <RefineKbarProvider>
        <ColorModeContextProvider>
          <AntdApp>
            <DevtoolsProvider>
              <Refine
                dataProvider={customDataProvider()}
                notificationProvider={useNotificationProvider}
                authProvider={authProvider}
                routerProvider={routerBindings}
                resources={[
                  {
                    name: "admin",
                    list: "/admin",
                    show: "/admin/show/:id",
                    create: "/admin/create",
                    edit: "/admin/edit/:id",
                    meta: {
                      label: "管理员",
                      icon: <FaUserShield />,
                    },
                  },

                  {
                    name: "user",
                    list: "/user",
                    show: "/user/show/:id",
                    create: "/user/create",
                    edit: "/user/edit/:id",
                    meta: {
                      label: "用户",
                      icon: <FaUser />,
                    },
                  },
                  {
                    name: "agent",
                    list: "/agent",
                    show: "/agent/show/:id",
                    create: "/agent/create",
                    edit: "/agent/edit/:id",
                    meta: {
                      label: "智能体",
                      icon: <FaRobot />,
                    },
                  },
                  {
                    name: "course",
                    list: "/course",
                    create: "/course/create",
                    show: "/course/show/:id",
                    edit: "/course/edit/:id",
                    meta: {
                      label: "课程管理",
                      icon: <FaGraduationCap />,
                    },
                  },
                  {
                    name: "order",
                    list: "/order",
                    show: "/order/show/:id",
                    edit: "/order/edit/:id",
                    meta: {
                      label: "订单",
                      icon: <FaMoneyBillWave />,
                    },
                  },
                  {
                    name: "paymentPlan",
                    list: "/paymentPlan",
                    create: "/paymentPlan/create",
                    show: "/paymentPlan/show/:id",
                    edit: "/paymentPlan/edit/:id",
                    meta: {
                      label: "付费计划",
                      icon: <FaFileInvoiceDollar />,
                    },
                  },
                ]}
                options={{
                  syncWithLocation: true,
                  warnWhenUnsavedChanges: true,
                  useNewQueryKeys: true,
                  projectId: "RUkgr3-hlbLLB-lUpTgX",
                  title: { text: APP_TITLE, icon: <IoLogoOctocat /> },
                }}
              >
                <Routes>
                  <Route
                    element={
                      <Authenticated
                        key="authenticated-inner"
                        fallback={<CatchAllNavigate to="/login" />}
                      >
                        <ThemedLayoutV2
                          Header={Header}
                          Sider={(props) => <ThemedSiderV2 {...props} fixed />}
                        >
                          <Outlet />
                        </ThemedLayoutV2>
                      </Authenticated>
                    }
                  >
                    <Route
                      index
                      element={<NavigateToResource resource="hiaAdmin" />}
                    />
                    <Route path="/admin">
                      <Route index element={<AdminList />} />
                      <Route path="create" element={<AdminCreate />} />
                      <Route path="edit/:id" element={<AdminEdit />} />
                      <Route path="show/:id" element={<AdminShow />} />
                    </Route>
                    <Route path="/user">
                      <Route index element={<UserList />} />
                      <Route path="create" element={<UserCreate />} />
                      <Route path="edit/:id" element={<UserEdit />} />
                      <Route path="show/:id" element={<UserShow />} />
                    </Route>
                    <Route path="/agent">
                      <Route index element={<AgentList />} />
                      <Route path="create" element={<AgentCreate />} />
                      <Route path="edit/:id" element={<AgentEdit />} />
                      <Route path="show/:id" element={<AgentShow />} />
                    </Route>
                    <Route path="/order">
                      <Route index element={<OrderList />} />
                      <Route path="edit/:id" element={<OrderEdit />} />
                      <Route path="show/:id" element={<OrderShow />} />
                    </Route>
                    <Route path="/paymentPlan">
                      <Route index element={<PaymentPlanList />} />
                      <Route path="create" element={<PaymentPlanCreate />} />
                      <Route path="edit/:id" element={<PaymentPlanEdit />} />
                      <Route path="show/:id" element={<PaymentPlanShow />} />
                    </Route>
                    <Route path="/course">
                      <Route index element={<CourseList />} />
                      <Route path="create" element={<CourseCreate />} />
                      <Route path="edit/:id" element={<CourseEdit />} />
                      <Route path="show/:id" element={<CourseShow />} />
                    </Route>
                    <Route path="*" element={<ErrorComponent />} />
                  </Route>
                  <Route
                    element={
                      <Authenticated
                        key="authenticated-outer"
                        fallback={<Outlet />}
                      >
                        <NavigateToResource />
                      </Authenticated>
                    }
                  >
                    <Route path="/login" element={<Login />} />
                  </Route>
                </Routes>

                <RefineKbar />
                <UnsavedChangesNotifier />
                <DocumentTitleHandler />
              </Refine>
              <DevtoolsPanel />
            </DevtoolsProvider>
          </AntdApp>
        </ColorModeContextProvider>
      </RefineKbarProvider>
    </HashRouter>
  );
}

export default App;
