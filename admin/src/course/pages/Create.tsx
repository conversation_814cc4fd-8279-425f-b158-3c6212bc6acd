import { Create, useForm } from "@refinedev/antd";
import {
  Form,
  Input,
  Upload,
  Select,
} from "antd";
import React from "react";
import { entityKey } from "../consts";
import { useNavigation } from "@refinedev/core";

const { TextArea } = Input;

const CourseCreate: React.FC = () => {
  const { formProps, saveButtonProps } = useForm({
    resource: entityKey,
  });

  const { goBack } = useNavigation();

  return (
    <Create
      saveButtonProps={saveButtonProps}
      headerButtons={[
        <button key="cancel" onClick={() => goBack()} className="ant-btn">
          取消
        </button>,
      ]}
    >
      <Form<any> {...formProps} layout="vertical">
        <Form.Item
          label="课程标题"
          name="title"
          rules={[{ required: true, message: "请输入课程标题" }]}
        >
          <Input placeholder="请输入课程标题" />
        </Form.Item>

        <Form.Item
          label="课程描述"
          name="description"
          rules={[{ required: true, message: "请输入课程描述" }]}
        >
          <TextArea 
            rows={4} 
            placeholder="请输入课程描述" 
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Form.Item
          label="导师"
          name="instructor"
        >
          <Input placeholder="请输入导师名称" />
        </Form.Item>

        <Form.Item
          label="课程封面"
          name="cover_image"
        >
          <Input placeholder="请输入课程封面图片URL" />
        </Form.Item>

        <Form.Item
          label="海报图片"
          name="poster_url"
        >
          <Input placeholder="请输入海报图片URL" />
        </Form.Item>

        <Form.Item
          label="标签"
          name="tags"
        >
          <Select
            mode="tags"
            placeholder="请输入标签，按回车添加"
            style={{ width: '100%' }}
            tokenSeparators={[',']}
          />
        </Form.Item>
      </Form>
    </Create>
  );
};

export default CourseCreate;
