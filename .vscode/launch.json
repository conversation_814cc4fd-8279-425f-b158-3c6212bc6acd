{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "调试客户端api",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "cwd": "${workspaceFolder}/api",
            "env": {
                "TYPE": "client",
                "PYTHONPATH": "${workspaceFolder}/api"
            },
            "args": [
                "app.main:app",
                "--reload"
            ],
            "jinja": true
        },
        {
            "name": "调试管理端api",
            "type": "debugpy",
            "cwd": "${workspaceFolder}/api",
            "request": "launch",
            "module": "uvicorn",
            "env": {
                "TYPE": "admin",
                "PYTHONPATH": "${workspaceFolder}/api"
            },
            "args": [
                "app.main:app",
                "--reload"
            ],
            "jinja": true
        },

        {
            "name": "调试客户端",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:1420",
            "webRoot": "${workspaceFolder}/client"
        }
    ]
}
