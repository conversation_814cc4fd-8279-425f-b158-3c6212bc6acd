import json
import re
from pathlib import Path

import requests
from lxml import etree
from pyper import task

home = 'https://e-hentai.org'

proxy = 'http://localhost:7890'

proxies = {
    'http': proxy,
    'https': proxy,
}

headers = {
    "Cookie": """ipb_member_id=8886661; ipb_pass_hash=537e5abd4c5b79679fb7c4b9bbaf8626; sk=lycw3nzmrnfgdyk8ts5gkii57eq5; tagaccept=1; nw=1; cf_clearance=wTklc_LeQCceP5xBwRXX7FXwJQkHqok7ZRTO6a36V7o-1746893476-*******-bRgzF26rPq8oHDmwJLn0TVtw3_GYF_RXFKdG83LVL6kTCBGdWkBu7Gqr59UrMPRKjprEoIVzFa9w_C3QrHIYm8eYgx7QqEtecvztEpRcfqEaZJZGcu4b.mRD4UTpeBX9Mjrc2f.Qv_iNVyfpXFczlnTRwt3wqpMgHKvkzU9y7wh85NCcqeZQur0UYaieVxGlgUHeMTtfcaAmAKWLX9VzKWZvlXIfHIGXRQ2Fr6oaIwJ9bDEgBACe6eYHMohzeVQgiAxZ6LQj3n4G6lP5mv21uDyX5hv69QzDHhqvFv.uvuhVDmPenHw0DSf_7Ey.T5FlXHrwjUZ2R1r8usaGNsqegWxuWgpgq20i6lyZCpyu0gRKvxy73pdBP8AY1GUXe3d_; star=1-287cfc3348; hath_perks=a.t1.m1-8e6dd304fd; ipb_session_id=4705bfb92c324d067d92b12339d7d2af; event=1747965542"""
}


def add_task_to_motrix(url, save_title):
    """
    添加下载任务给motrix
    :param url: 下载链接
    :param save_title: 保存名称
    :return:
    """
    data = json.dumps({'jsonrpc': '2.0', 'id': 'qwer',
                       'method': 'aria2.addUri',
                       'params': [
                           "token:DNmBvkXrj2Gs",
                           [url],
                           {'out': save_title,
                            'dir': str(save_path)}

                       ]
                       })
    c = requests.post('http://localhost:16800/jsonrpc', data)
    return c.json()


def downTorrent(gldown: str):
    """
    下载磁力文件
    :param gldown: 磁力文件页面，https://e-hentai.org/gallerytorrents.php?gid=3141655&t=7e5006ee9b
    :return:
    """
    # 前往磁力页面
    response = requests.get(gldown, proxies=proxies, headers=headers)
    assert response.status_code == 200
    responseHtml = etree.HTML(response.text)
    # 定位元素
    torrent = responseHtml.xpath("//td[@colspan='5']/a/@href")[0]
    title = responseHtml.xpath("//td[@colspan='5']/a/text()")[0]

    # 构造磁力文件路径
    token = re.search(r'(?<=&t=)\w{10}(?!=\.)', gldown).group()
    save_name = save_path.joinpath(f"{title}{token}.torrent")
    if save_name.exists():
        return '已存在' + title
    # 请求磁力文件数据
    response = requests.get(torrent, proxies=proxies, headers=headers)
    assert response.status_code == 200
    with open(save_name, 'wb') as f:
        f.write(response.content)

    return title


def downloadComic(gl1t: str):
    """
    下载普通的漫画
    :param gl1t: 漫画主页，https://e-hentai.org/g/3141655/7e5006ee9b/
    :return:
    """
    # 获取日语名称
    response = requests.get(gl1t, headers=headers, proxies=proxies)
    try:
        title = etree.HTML(response.text).xpath("//h1[@id='gj']/text()")[0]
    except IndexError as e:
        # 如果没有就使用英文标题
        title = etree.HTML(response.text).xpath("//h1[@id='gn']/text()")[0]
    # 构造存档页面请求
    numbers = re.findall(r'/g/(\w+)/(\w+)/', gl1t)[0]
    archiver_page = f'https://e-hentai.org/archiver.php?gid={numbers[0]}&token={numbers[1]}'
    response = requests.post(archiver_page, data={"dltype": "org", "dlcheck": "Download Original Archive"},
                             headers=headers, proxies=proxies)
    # 定位请求地址
    link = etree.HTML(response.text).xpath("//div[@id='db']/p/a/@href")[0] + '?start=1'

    # 清洗文件标题
    title = title.replace('?', '-').replace('/', '-')
    # 添加下载任务
    add_task_to_motrix(link, title)
    return title


def chooseDownloadType(url: str):
    """
    检查下载方式
    :param url: 期望的下载链接
    :return:
    """
    if 'gallerytorrents' in url:
        return downTorrent(url)
    else:
        return downloadComic(url)


def search(key, chinese=True):
    def search_by_url(url: str):
        response = requests.get(url, proxies=proxies, headers=headers)
        print(response.status_code)
        responseHtml = etree.HTML(response.text)
        # 磁力下载页面
        works = responseHtml.xpath("//div[@class='itg gld']")[0]
        gldown_urls = []
        for work in works:
            try:
                # 检查是否有磁力
                torrent_page = work.xpath("./div[2]/div[2]/div[3]/a/@href")[0]
                gldown_urls.append(torrent_page)
            except IndexError as e:
                # 否则执行普通购买下载
                gldown_urls.append(work.xpath("./a/@href")[0])

        # 判断是否有下一页
        next_page = responseHtml.xpath("//a[@id='dnext']/@href")
        if len(next_page) != 0:
            # 递归
            gldown_urls.extend(search_by_url(next_page[0]))
            # print(gldown_urls)
        return gldown_urls

    search_url = f'{home}/?f_search=language:chinese%24+{key}' if chinese else f'https://e-hentai.org/?f_search={key}'

    # 依次返回结果处理
    for i in search_by_url(search_url):
        yield i


if __name__ == '__main__':
    pipe = task(search, branch=True) | task(chooseDownloadType, workers=5)

    # search_key = input("请输入搜索关键词：")
    # save_path = Path(f'/home/<USER>/Downloads/{input("请输入保存文件夹名称：")}')
    search_key = '''a:"hatsuden pengin$" '''
    save_path = Path(f'/home/<USER>/Downloads/発電ペンギン')

    save_path.mkdir(parents=True, exist_ok=True)
    for i in pipe(search_key):
        print(i)

    # downloadComic('https://e-hentai.org/g/1240723/e1cbb46abe/')
    # for i in search(search_key):
    #     print(i)
