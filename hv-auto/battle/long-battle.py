from playwright.sync_api import sync_playwright
import time


def auto_combat():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto("https://hentaiverse.org/?s=Battle")
        page.wait_for_load_state("networkidle")

        while True:
            # 检查敌人列表是否存在
            enemy_list = page.wait_for_selector("//div[@id='pane_monster']", timeout=5000)
            if not enemy_list:
                print("战斗结束")
                break

            # 获取所有敌人子 div
            enemies = enemy_list.query_selector_all("div")
            if not enemies:
                print("没有找到敌人，战斗可能已结束")
                break

            for enemy in enemies:
                # 检查是否有 onclick 属性
                while enemy.get_attribute("onclick"):
                    enemy.click()
                    time.sleep(0.5)  # 等待攻击生效
                print("敌人死亡")

            time.sleep(1)  # 等待下一场战斗加载

        browser.close()


if __name__ == "__main__":
    auto_combat()