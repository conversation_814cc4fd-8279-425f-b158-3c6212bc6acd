[project]
name = "aq-api"
version = "0.1.0"
description = "ai智元后台"
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=24.1.0",
    "alembic==1.14.1",
    "alibabacloud-dysmsapi20170525>=3.1.1",
    "annotated-types==0.7.0",
    "anyio==4.8.0",
    "apscheduler>=3.11.0",
    "autoflake>=2.3.1",
    "bcrypt==4.2.1",
    "click==8.1.8",
    "colorama==0.4.6",
    "dify-sdk>=0.1.12",
    "fastapi==0.115.8",
    "greenlet==3.1.1",
    "h11==0.14.0",
    "hkjx-billing-sdk>=0.1.2",
    "httptools==0.6.4",
    "httpx>=0.28.1",
    "idna==3.10",
    "inflection>=0.5.1",
    "loguru==0.7.3",
    "mako==1.3.9",
    "markupsafe==3.0.2",
    "oss2>=2.19.1",
    "psycopg2-binary>=2.9.10",
    "pydantic==2.10.6",
    "pydantic-core==2.27.2",
    "pydantic-settings==2.7.1",
    "pyfiglet>=1.0.2",
    "pyjwt==2.10.1",
    "python-alipay-sdk>=3.3.0",
    "python-dotenv==1.0.1",
    "python-multipart>=0.0.20",
    "pyyaml==6.0.2",
    "redis>=5.2.1",
    "rich>=13.9.4",
    "schedule>=1.2.2",
    "sentry-sdk>=2.24.1",
    "sniffio==1.3.1",
    "sqlalchemy==2.0.38",
    "sqlmodel==0.0.22",
    "sshtunnel>=0.4.0",
    "starlette==0.45.3",
    "typing-extensions==4.12.2",
    "uvicorn[standard]==0.34.0",
    "watchfiles==1.0.4",
    "websockets==14.2",
    "wechatpy>=1.8.18",
    "win32-setctime==1.2.0",
    "wrapt>=1.17.2",
]

[dependency-groups]
dev = [
    "pre-commit>=4.2.0",
]
