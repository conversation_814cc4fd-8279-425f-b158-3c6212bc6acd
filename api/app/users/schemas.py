from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field

from app.db.models import Agent, UserAsset

AssetType = UserAsset.Type
AgentType = Agent.Type


class UserAssetItem(BaseModel):
    """用户资产项"""

    asset_type: AssetType = Field(..., description="资产类型")
    asset_id: str = Field(..., description="资产ID")
    asset_name: str = Field(..., description="资产名称")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")
    details: Optional[dict] = Field(
        default=None, description="资产详情，根据类型不同结构不同"
    )
    quantity: Optional[int] = Field(default=None, description="数量")
    expire_at: Optional[datetime] = Field(default=None, description="过期时间")
