from fastapi import APIRouter, Body, Depends, HTTPException, Path
from sqlmodel import Session, delete, select

from app.admin.utils import get_current_admin
from app.core import ResponsePayloads
from app.core import settings
from app.db import get_session
from app.db.models import Admin, Agent, Course, User, UserAsset
from billing import HkJingXiuBilling
from billing.keys import KeyListResponse, KeyCreate

from .schemas import BatchAssignRequest, BatchAssignResponse

router = APIRouter(prefix="/users", tags=["用户"])


@router.post(
    "/assets/batch_assign", summary="批量分配资产", response_model=BatchAssignResponse
)
async def batch_assign(
    request: BatchAssignRequest = Body(...),
    current_admin: Admin = Depends(get_current_admin),
    session: Session = Depends(get_session),
):
    """批量分配资产"""
    try:
        # 验证用户是否存在
        user = session.exec(select(User).where(User.id == request.user_id)).first()
        if not user:
            raise HTTPException(
                status_code=404, detail=f"用户ID {request.user_id} 不存在"
            )

        success_count = 0

        # 先获取用户当前所有指定类型的资产
        existing_assets = session.exec(
            select(UserAsset).where(
                UserAsset.user_id == request.user_id,
                UserAsset.asset_type == request.asset_type,
            )
        ).all()

        # 创建当前资产ID集合和请求资产ID集合
        current_asset_ids = {asset.asset_id for asset in existing_assets}
        requested_asset_ids = set(request.asset_ids)

        # 找出需要删除的资产ID（在当前集合中但不在请求集合中）
        to_remove_asset_ids = current_asset_ids - requested_asset_ids

        # 删除不在请求中的资产
        if to_remove_asset_ids:
            for asset_id in to_remove_asset_ids:
                # 从数据库中删除该资产
                session.exec(
                    delete(UserAsset).where(
                        UserAsset.user_id == request.user_id,
                        UserAsset.asset_type == request.asset_type,
                        UserAsset.asset_id == asset_id,
                    )
                )

        for asset_id in request.asset_ids:
            # 根据资产类型查询资产是否存在
            if request.asset_type == UserAsset.Type.APP:
                asset = session.exec(select(Agent).where(Agent.id == asset_id)).first()
                app_mode = asset.type if asset else None
                asset_name = asset.name if asset else None
            elif request.asset_type == UserAsset.Type.COURSE:
                asset = session.exec(
                    select(Course).where(Course.id == asset_id)
                ).first()
                app_mode = None
                asset_name = asset.title if asset else None
            else:
                raise HTTPException(status_code=400, detail="不支持的资产类型")

            if not asset:
                continue

            # 查询用户是否已经拥有该资产
            existing_asset = session.exec(
                select(UserAsset).where(
                    UserAsset.user_id == request.user_id,
                    UserAsset.asset_type == request.asset_type,
                    UserAsset.asset_id == asset_id,
                )
            ).first()

            if not existing_asset:
                # 创建新的用户资产
                new_asset = UserAsset(
                    user_id=request.user_id,
                    asset_type=request.asset_type,
                    asset_id=asset_id,
                    asset_name=asset_name,
                    app_mode=app_mode,
                )
                session.add(new_asset)

            success_count += 1

        session.commit()
        return BatchAssignResponse(success=True)

    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=500, detail=f"分配资产失败: {str(e)}")


@router.get(
    "/{user_id}/keys",
    summary="获取用户密钥列表",
    response_model=ResponsePayloads[KeyListResponse],
)
async def get_user_keys(
    user_id: int = Path(..., description="用户ID"),
    current_admin: Admin = Depends(get_current_admin),
):
    """获取用户密钥列表"""
    try:
        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 获取用户所有密钥
        keys_response = await billing_client.keys.list_available_keys(user_id=str(user_id))

        # 对于每个密钥，如果是智能体密钥，获取智能体名称
        for key in keys_response.keys:
            if key.service_code == "AGC" and key.instance_id:
                try:
                    session = next(get_session())

                    # 如果是多个实例ID（以逗号分隔）
                    if ',' in key.instance_id:
                        instance_ids = [id.strip() for id in key.instance_id.split(',')]
                        instance_names = []

                        # 获取每个实例ID对应的名称
                        for instance_id in instance_ids:
                            agent = session.exec(
                                select(Agent).where(Agent.id == instance_id)
                            ).first()

                            if agent and agent.name:
                                instance_names.append(agent.name)
                            else:
                                # 如果找不到名称，就使用ID
                                instance_names.append(instance_id)

                        # 将多个实例名称以逗号连接
                        key.instance_name = ",".join(instance_names)
                    else:
                        # 单个实例ID的情况
                        agent = session.exec(
                            select(Agent).where(Agent.id == key.instance_id)
                        ).first()
                        if agent:
                            key.instance_name = agent.name
                except Exception as e:
                    # 如果查询失败，记录错误并继续处理
                    print(f"获取实例名称失败: {str(e)}")
                    pass

        return ResponsePayloads(data=keys_response)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户密钥失败: {str(e)}")


@router.delete(
    "/{user_id}/keys/{key_id}",
    summary="删除用户密钥",
    response_model=ResponsePayloads[dict],
)
async def delete_user_key(
    user_id: int = Path(..., description="用户ID"),
    key_id: str = Path(..., description="密钥ID"),
    current_admin: Admin = Depends(get_current_admin),
):
    """删除用户密钥"""
    try:
        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 删除密钥
        await billing_client.keys.delete_key(key_id=key_id)

        return ResponsePayloads(data={"success": True})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除密钥失败: {str(e)}")


@router.post(
    "/{user_id}/keys",
    summary="创建用户密钥",
    response_model=ResponsePayloads[dict],
)
async def create_user_key(
    user_id: int = Path(..., description="用户ID"),
    key_data: KeyCreate = Body(..., description="密钥创建数据"),
    current_admin: Admin = Depends(get_current_admin),
):
    """创建用户密钥"""
    try:
        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 设置用户ID
        key_data.user_id = str(user_id)

        # 创建密钥
        key = await billing_client.keys.create_key(key_data)

        return ResponsePayloads(data={"id": key.id, "success": True})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建密钥失败: {str(e)}")
