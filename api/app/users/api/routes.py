import random
import string
from datetime import datetime

from fastapi import APIRouter, Body, Depends, HTTPException, Query
from sqlmodel import Session, func, or_, select

from app.core import (
    Pagination,
    ResponsePayloads,
    create_jwt_token,
    default_password_encoder,
    redis_client,
    sms_service,
)
from app.db import User, get_session
from app.db.models import Agent, Course, UserAsset
from .schemas import (
    LoginRequest,
    LoginResult,
    RegisterRequest,
    ResetPasswordRequest,
    SmsLoginRequest,
    UserAssetQuery,
    UserInfo,
)
from .utils import get_current_user
from ..schemas import AgentType, AssetType, UserAssetItem

router = APIRouter(prefix="/users", tags=["用户"])


@router.post(
    "/login", summary="用户名密码登录", response_model=ResponsePayloads[LoginResult]
)
async def login(
        data: LoginRequest = Body(...), session: Session = Depends(get_session)
):
    """用户名密码登录"""
    # 查找用户
    statement = select(User).where(
        or_(User.username == data.username, User.phone == data.username)
    )
    user = session.exec(statement).first()

    # 用户不存在或密码错误
    if not user or not default_password_encoder.matches(data.password, user.password):
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    # 生成token
    token = create_jwt_token({"sub": str(user.id)})

    return ResponsePayloads(
        data=LoginResult(
            token=token,
            user_info=UserInfo(
                id=user.id,
                username=user.phone if user.phone else user.username,
                can_create_agent=user.can_create_agent,
            ),
        )
    )


@router.get(
    "/me", summary="获取当前用户信息", response_model=ResponsePayloads[UserInfo]
)
async def get_me(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return ResponsePayloads(
        data=UserInfo(
            id=current_user.id,
            username=(
                current_user.phone if current_user.phone else current_user.username
            ),
            can_create_agent=current_user.can_create_agent,
        )
    )


@router.post(
    "/login/sms", summary="短信验证码登录", response_model=ResponsePayloads[LoginResult]
)
async def login_with_sms(
        data: SmsLoginRequest = Body(...), session: Session = Depends(get_session)
):
    """短信验证码登录"""
    # 查找用户
    statement = select(User).where(User.phone == data.phone)
    user = session.exec(statement).first()

    # 从redis中获取验证码进行比对
    stored_code = redis_client.get(f"sms_code:{data.phone}")
    if not stored_code or stored_code != data.code:
        raise HTTPException(status_code=401, detail="验证码错误或已过期")

    # 如果用户不存在，自动创建用户
    if not user:
        # 生成随机用户名（使用手机号后4位加上随机字符）
        random_suffix = "".join(
            random.choices(string.ascii_lowercase + string.digits, k=4)
        )
        username = f"user_{data.phone[-4:]}_{random_suffix}"

        # 生成随机密码
        random_password = "".join(
            random.choices(string.ascii_letters + string.digits, k=12)
        )

        # 创建新用户
        user = User(
            username=username,
            password=default_password_encoder.encode(random_password),
            phone=data.phone,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

        session.add(user)
        session.commit()
        session.refresh(user)

    # 生成token
    token = create_jwt_token({"sub": str(user.id)})

    return ResponsePayloads(
        data=LoginResult(
            token=token,
            user_info=UserInfo(
                id=user.id,
                username=user.phone if user.phone else user.username,
                can_create_agent=user.can_create_agent,
            ),
        )
    )


@router.get("/send_verification_code", summary="发送验证码")
async def send_verification_code(phone: str = Query(..., description="手机号")):
    """发送短信验证码"""
    # 生成6位随机数字验证码
    code = "".join(random.choices(string.digits, k=6))

    # 将验证码存储到redis，设置5分钟过期时间
    redis_client.set(f"sms_code:{phone}", code, ex=300)

    # 发送短信
    template_param = {"code": code}
    success = sms_service.send_sms(phone, template_param)

    if not success:
        raise HTTPException(status_code=500, detail="短信发送失败")

    return ResponsePayloads(data="success")


@router.post(
    "/register", summary="用户注册", response_model=ResponsePayloads[LoginResult]
)
async def register(
        data: RegisterRequest = Body(...), session: Session = Depends(get_session)
):
    """用户注册"""
    # 检查用户名是否已存在
    statement = select(User).where(User.username == data.username)
    existing_user = session.exec(statement).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")

    # 检查手机号是否已注册
    statement = select(User).where(User.phone == data.phone)
    existing_phone = session.exec(statement).first()
    if existing_phone:
        raise HTTPException(status_code=400, detail="手机号已注册")

    # 检查验证码
    stored_code = redis_client.get(f"sms_code:{data.phone}")
    if not stored_code or stored_code != data.code:
        raise HTTPException(status_code=400, detail="验证码错误或已过期")

    # 创建新用户
    new_user = User(
        username=data.username,
        password=default_password_encoder.encode(data.password),
        phone=data.phone,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )

    session.add(new_user)
    session.commit()
    session.refresh(new_user)

    # 生成登录token
    token = create_jwt_token({"sub": str(new_user.id)})

    return ResponsePayloads(
        data=LoginResult(
            token=token,
            user_info=UserInfo(
                id=new_user.id,
                username=data.phone,
                can_create_agent=new_user.can_create_agent,
            ),
        )
    )


@router.post("/reset-password", summary="重置密码")
async def reset_password(
        data: ResetPasswordRequest = Body(...), session: Session = Depends(get_session)
):
    """通过短信验证码重置密码"""
    # 查找用户
    statement = select(User).where(User.phone == data.phone)
    user = session.exec(statement).first()

    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 验证短信验证码
    stored_code = redis_client.get(f"sms_code:{data.phone}")
    if not stored_code or stored_code != data.code:
        raise HTTPException(status_code=401, detail="验证码错误或已过期")

    # 更新密码
    user.password = default_password_encoder.encode(data.new_password)
    user.updated_at = datetime.now()
    session.add(user)
    session.commit()

    return ResponsePayloads(data="success")


@router.get(
    "/assets",
    summary="获取用户资产",
    response_model=ResponsePayloads[Pagination[UserAssetItem]],
)
async def get_user_assets(
        query: UserAssetQuery = Depends(),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """获取用户资产列表"""
    # 构建基础查询
    statement = select(UserAsset).where(UserAsset.user_id == current_user.id)

    # 按资产名称过滤
    if query.asset_name:
        statement = statement.where(UserAsset.asset_name.ilike(f"%{query.asset_name}%"))

    # 按资产类型过滤
    if query.asset_type:
        statement = statement.where(UserAsset.asset_type == query.asset_type)
    # 按应用模式过滤
    if query.app_mode:
        if query.app_mode == AgentType.CHAT:
            statement = statement.where(
                UserAsset.app_mode.in_([AgentType.CHAT, AgentType.AGENT_CHAT])
            )
        else:
            statement = statement.where(UserAsset.app_mode == query.app_mode)
    # 计算总数
    total = session.exec(select(func.count()).select_from(statement)).one()

    # 分页查询
    items = session.exec(
        statement.order_by(UserAsset.created_at.desc())
        .offset((query.page - 1) * query.page_size)
        .limit(query.page_size)
    ).all()

    # 按需查询详情
    asset_details = {}
    if query.include_details:  # 只有当需要详情时才查询
        for item in items:
            if item.asset_type == AssetType.APP:
                app = session.get(Agent, item.asset_id)
                if app:
                    asset_details[item.id] = {
                        "name": app.name,
                        "icon_url": app.icon_url,
                        "description": app.description,
                        "mode": app.type.value,
                        "owner_id": app.owner_id,
                    }
            elif item.asset_type == AssetType.COURSE:
                course = session.get(Course, item.asset_id)
                if course:
                    asset_details[item.id] = {
                        "title": course.title,
                        "description": course.description,
                        "price": course.price,
                        "cover_image": course.cover_image,
                    }

    return ResponsePayloads(
        data=Pagination(
            data=[
                UserAssetItem(
                    asset_type=item.asset_type,
                    asset_id=item.asset_id,
                    asset_name=item.asset_name,
                    created_at=item.created_at,
                    details=(
                        asset_details.get(item.id) if query.include_details else None
                    ),
                )
                for item in items
            ],
            total=total,
            has_more=(query.page * query.page_size) < total,
            page_no=query.page,
            page_size=query.page_size,
        )
    )