from billing import HkJingXiuBilling
from billing.keys import KeyConsumeRequest, CurrencyType
from dify import AdminClient, Dify
from fastapi import HTTPException
from httpx import AsyncClient
import wrapt
from dify.app.schemas import MessageEndEvent
from app.core import chat_logger, settings
from app.db.models import User

admin_client = AdminClient(settings.dify_base_url, settings.dify_admin_key)
dify = Dify(admin_client)


def require_chat_permission(
    currenices: list[CurrencyType] = [
        CurrencyType.CNY,
        CurrencyType.LTC,
        CurrencyType.UTS,
    ]
):
    """
    装饰器：检查用户是否拥有智能体的密钥权限

    此装饰器会检查当前用户是否在商户平台上拥有指定智能体的使用权限。
    通过查询用户资产表来确认用户是否有权限使用该智能体。

    用法:
    @require_agent_key_permission()
    async def some_function(agent_id: str, current_user: User, session: Session):
        # 函数实现

    :raises:
        HTTPException: 如果用户没有权限使用该智能体，返回403错误
    """

    @wrapt.decorator
    async def wrapper(wrapped, instance, args, kwargs):
        # 从依赖注入获取当前用户、会话和智能体ID
        current_user: User = kwargs.get("current_user")
        if not current_user:
            raise HTTPException(status_code=403, detail="未授权访问")

        session = kwargs.get("session")
        if not session:
            raise HTTPException(status_code=500, detail="数据库会话不可用")

        # 先尝试获取agent_id参数
        agent_id = kwargs.get("agent_id")

        # 如果没有agent_id，尝试获取app_id参数（某些接口使用app_id而非agent_id）
        if not agent_id:
            agent_id = kwargs.get("app_id")

        # 如果还是没有，尝试从位置参数获取
        if not agent_id:
            for i, arg in enumerate(args):
                if (
                    isinstance(arg, str) and len(arg) > 10
                ):  # 假设agent_id是一个较长的字符串
                    agent_id = arg
                    break

        if not agent_id:
            raise HTTPException(status_code=400, detail="缺少智能体ID参数")

        # 检查用户是否拥有该智能体的资产
        chat_logger.info(f"检查用户 {current_user.id} 对智能体 {agent_id} 的权限")
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )
        available_keys = []
        for currency in currenices:
            keys = await billing_client.keys.list_available_keys(
                user_id=str(current_user.id),
                service_code="AGC",
                # instance_id=agent_id,
                currency_type=currency.value,
            )
            available_keys.extend(keys.keys)
            if len(keys.keys) > 0:
                break

        if len(available_keys) == 0:
            raise HTTPException(status_code=403, detail="没有权限使用该智能体")

        # 用户有权限，继续执行原函数
        return await wrapped(*args, **kwargs)

    return wrapper


async def transcribe_audio(file_path: str) -> str:
    """将音频文件转换为文本"""
    try:
        async with AsyncClient() as client:
            with open(file_path, "rb") as audio_file:
                files = {"file": ("audio.mp3", audio_file, "audio/mpeg")}
                response = await client.post(
                    "http://difytools.cruldra.cn/audios/transcribe",
                    files=files,
                    headers={"accept": "application/json"},
                )

            if response.status_code != 200:
                chat_logger.error(f"语音转文本失败: {response.text}")
                raise HTTPException(
                    status_code=response.status_code, detail="语音转文本服务异常"
                )

            return response.json()["data"]["text"]

    except Exception as e:
        chat_logger.exception(f"语音转文本时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="语音转文本失败")


async def consume_key(user_id: int, agent_id: str, message_end_event: MessageEndEvent):
    chat_logger.info(f"上报用户 {user_id} 使用智能体 {agent_id} 的令牌消耗")
    billing_client = HkJingXiuBilling(
        base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
    )
    available_keys = await billing_client.keys.list_available_keys(
        user_id=str(user_id), service_code="AGC", 
        # instance_id=agent_id
    )
    key = available_keys.keys[0]
    await billing_client.keys.consume_key(
        KeyConsumeRequest(
            key_id=key.id,
            details =message_end_event.metadata.usage.model_dump(),
        )
    )
