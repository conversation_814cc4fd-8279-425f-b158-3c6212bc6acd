from datetime import datetime
from typing import List

from fastapi import APIRouter, Body, Depends, Query
from sqlmodel import Session, select

from app.admin.utils import get_current_admin
from app.agents.api.schemas import AgentBase
from app.core import Pagination
from app.core.schemas import ResponsePayloads
from app.db import get_session
from app.db.models import Admin, Agent

from ..utils import dify
from .schemas import AppImportStatus, ImportAppsResponse

router = APIRouter(prefix="/agents", tags=["智能体"])


@router.get(
    "/from_dify",
    summary="从Dify获取应用列表",
    response_model=ResponsePayloads[Pagination[AgentBase]],
)
async def get_dify_apps(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    limit: int = Query(10, ge=1, le=100, description="每页数量"),
    name: str = Query("", description="名称过滤"),
    current_admin: Admin = Depends(get_current_admin),
):
    """获取Dify应用列表"""
    dify_apps_page = await dify.app.find_list(
        page=page,
        limit=limit,
        name=name,
    )
    agents = [AgentBase.from_dify_app(app) for app in dify_apps_page.data]
    # 构造分页响应
    return ResponsePayloads(
        data=Pagination(
            data=agents,
            page_size=dify_apps_page.limit,
            page_no=dify_apps_page.page,
            total=dify_apps_page.total,
            has_more=dify_apps_page.has_more,
        )
    )


class ImportAgentsResult(ResponsePayloads):
    """导入智能体结果"""

    success_count: int
    failed_count: int
    failed_details: List[str]


@router.post(
    "/import",
    summary="导入智能体",
    response_model=ResponsePayloads[ImportAppsResponse],
    description="批量导入智能体，接受AgentBase列表",
)
async def import_agents(
    agents: List[AgentBase] = Body(..., description="智能体列表"),
    current_admin: Admin = Depends(get_current_admin),
    session: Session = Depends(get_session),
):
    """导入智能体接口

    接受一个智能体列表，将它们导入到系统中
    如果指定了用户ID，同时也会为该用户创建对应的资产记录
    """
    imported_apps: List[AppImportStatus] = []

    for agent_data in agents:
        try:
            # 检查是否已存在同ID的智能体
            existing_agent = session.exec(
                select(Agent).where(Agent.id == agent_data.id)
            ).first()

            # 获取或创建API密钥
            api_key = None
            if existing_agent and existing_agent.api_key:
                # 如果已存在且有API密钥，继续使用
                api_key = existing_agent.api_key
            else:
                # 尝试创建新API密钥
                try:
                    api_key_result = await dify.app.create_api_key(agent_data.id)
                    api_key = api_key_result.token
                except Exception:
                    # 继续处理，因为可能已经有API密钥
                    pass

            if existing_agent:
                # 如果存在，更新智能体信息
                existing_agent.name = agent_data.name
                existing_agent.type = agent_data.type
                existing_agent.updated_at = datetime.now()
                # 如果获取到了新的API密钥，则更新
                if api_key and not existing_agent.api_key:
                    existing_agent.api_key = api_key
                session.add(existing_agent)
                imported_apps.append(
                    AppImportStatus(id=agent_data.id, status="already_exists")
                )
            else:
                # 如果不存在，创建新智能体
                new_agent = Agent(
                    id=agent_data.id,
                    name=agent_data.name,
                    type=agent_data.type,
                    api_key=api_key,  # 设置API密钥
                )
                session.add(new_agent)
                imported_apps.append(
                    AppImportStatus(id=agent_data.id, status="imported")
                )
        except Exception:
            # 导入失败不添加到已导入列表中
            pass

    # 提交数据库事务
    session.commit()

    return ResponsePayloads(data=ImportAppsResponse(imported_apps=imported_apps))
