import logging
import os
import uuid
from datetime import datetime
from io import BytesIO
from pathlib import Path
from typing import List

from markitdown import MarkItDown
from pydantic import BaseModel

# OSS上传
try:
    import oss2
    from app.core.configs import settings
except ImportError:
    oss2 = None
# Word处理
try:
    from docx import Document
    from docx.oxml.ns import qn
except ImportError:
    docx = None
# PDF处理
try:
    import pdfplumber
except ImportError:
    pdfplumber = None

# 设置日志
logger = logging.getLogger(__name__)
# 忽略pdfminer的日志警告
logging.getLogger('pdfminer').setLevel(logging.ERROR)


class Data(BaseModel):
    """
    {
      "text":[
        "text1...",
        "[imgId]",
        "text2..."
      ],
      "imgs":{
        "imgId":"url",
        "imgId":"url"
      }
    }
    """
    text: List[str | int] = []
    imgs: List[str] = []


class FileProcessingError(Exception):
    """文件处理错误"""
    pass


class UnsupportedFileTypeError(Exception):
    """不支持的文件类型"""
    pass


def upload_image_to_oss(image_data: bytes, file_name: str = None) -> str:
    """
    将图片上传到OSS并返回URL

    Args:
        image_data: 图片二进制数据
        file_name: 文件名，如果为None则自动生成

    Returns:
        str: 上传后的图片URL

    Raises:
        FileProcessingError: 上传失败时抛出
    """
    if oss2 is None:
        raise ImportError("请安装oss2库以支持图片上传")

    try:
        # 生成文件名
        if file_name is None:
            timestamp = int(datetime.now().timestamp())
            random_str = uuid.uuid4().hex[:8]
            file_name = f"{timestamp}-{random_str}.png"

        # 初始化OSS客户端
        auth = oss2.Auth(settings.aliyun_access_key_id, settings.aliyun_access_key_secret)
        endpoint = "https://oss-cn-hangzhou.aliyuncs.com"
        bucket = oss2.Bucket(
            auth,
            endpoint,
            settings.oss_bucket,
            connect_timeout=30,
        )

        # 上传图片
        bucket.put_object(file_name, image_data)

        # 构建URL
        file_url = f"https://{settings.oss_bucket}.oss-cn-hangzhou.aliyuncs.com/{file_name}"
        logger.info(f"图片已上传到OSS: {file_url}")

        return file_url
    except Exception as e:
        logger.error(f"上传图片到OSS失败: {str(e)}")
        raise FileProcessingError(f"上传图片到OSS失败: {str(e)}")


def get_images_from_word(docx_path: str | Path) -> List[BytesIO]:
    """
    从Word文档中提取所有图片，使用Python标准库，返回图片字节数组。

    Args:
        docx_path (str): Word文档的路径

    Returns:
        list: 包含每个图片的BytesIO对象的列表
    """
    try:
        image_bytes_list = []
        doc = Document(docx_path)

        # 遍历文档中的所有段落和内联形状
        for paragraph in doc.paragraphs:
            for run in paragraph.runs:
                # 检查run中是否包含内联图片
                for inline_shape in run._element.findall('.//' + qn('w:drawing')):
                    blip = inline_shape.find('.//' + qn('a:blip'))
                    if blip is not None:
                        r_id = blip.get(qn('r:embed'))
                        if r_id:
                            # 获取图片数据
                            image_part = doc.part.related_parts.get(r_id)
                            if image_part:
                                image_bytes = image_part.blob
                                image_bytes_list.append(BytesIO(image_bytes))

        if not image_bytes_list:
            logger.info(f"未在 {docx_path} 中找到图片")

        logger.info(f"从 {docx_path} 中提取了 {len(image_bytes_list)} 张图片")
        return image_bytes_list

    except Exception as e:
        logger.error(f"处理文档时发生错误: {str(e)}")
        raise FileProcessingError(f"处理文档时发生错误: {str(e)}")


def word2markdown(file_path: str | Path) -> Data:
    """
    将word文档转换为markdown格式的文本

    Args:
        file_path (str): word文档的路径
    Returns:
        Data: markdown格式的文本
    """
    if docx is None:
        raise ImportError("请安装docx库以支持Word处理")

    md = MarkItDown(enable_plugins=False)
    result = md.convert(file_path).markdown.split("\n")
    # 处理文件夹杂图片的情况
    data = Data()
    imgIdx = 0
    base64Str = '![](data:image/png;base64...)'
    # 遍历每个元素
    for i in result:
        # 跳过空元素
        if not i:
            continue
        # 如果含图片
        if base64Str in i:
            splitTxt = i.split(base64Str)
            length = len(splitTxt)
            # 这个判断比较复杂，主要就是处理夹杂在文字之间的图片
            for j, txt in enumerate(splitTxt):
                if j == 0 and not txt:
                    continue
                if txt:
                    data.text.append(txt)
                    if j + 1 == length:
                        continue
                data.text.append(imgIdx)
                imgIdx += 1
        else:
            data.text.append(i)

    for img in get_images_from_word(file_path):
        img_data = img.read()
        img_url = upload_image_to_oss(img_data)
        data.imgs.append(img_url)

    return data


def pdf2markdown(file_path: str | Path) -> Data:
    """
    将PDF文档转换为Markdown格式的文本，并提取图片上传到OSS。

    Args:
        file_path (str): PDF文档的路径

    Returns:
        Data: 包含Markdown文本和图片URL的Data对象

    Raises:
        FileProcessingError: 文件处理失败时抛出
        ImportError: 缺少必要库时抛出
    """
    if pdfplumber is None:
        raise ImportError("请安装pdfplumber库以支持PDF处理")

    if oss2 is None:
        raise ImportError("请安装oss2库以支持图片上传")

    data = Data()
    img_idx = 0

    # 打开PDF文件
    with pdfplumber.open(file_path) as pdf:
        for page in pdf.pages:
            # 提取文本
            text = page.extract_text(layout=True)  # 使用layout模式保留格式
            if text:
                # 按行分割文本，清理空行和多余空格
                lines = [line.strip() for line in text.split('\n') if line.strip()]
                for line in lines:
                    data.text.append(line)

            # 提取图片
            for img in page.images:
                # 获取图片数据
                img_data = img['stream'].get_data()
                if img_data:
                    # 上传图片到OSS
                    img_url = upload_image_to_oss(img_data)
                    # 在文本列表中插入图片索引（模拟Markdown图片占位）
                    data.text.append(img_idx)
                    # 添加图片URL到imgs列表
                    data.imgs.append(img_url)
                    img_idx += 1

    return data


def office2markdown(file_path: str | Path) -> Data:
    """
    将office文档转换为markdown格式的文本

    Args:
        file_path (str): office文档的路径
    Returns:
        str: markdown格式的文本
    """
    # 判断文件类型
    suffix = os.path.splitext(file_path)[1]
    try:
        if suffix == '.docx':
            data = word2markdown(file_path)
        elif suffix == '.pdf':
            data = pdf2markdown(file_path)
        else:
            raise UnsupportedFileTypeError(f'不支持的文件类型{suffix}')
    except Exception as e:
        logger.error(f"处理文档[{file_path}]时发生错误: {str(e)}")
        raise FileProcessingError(f"处理文档[{file_path}]时发生错误: {str(e)}")

    # 封装返回结果
    return data
