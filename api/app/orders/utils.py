import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Union, Dict, Any

import httpx
from alipay import AliPay
from fastapi.responses import Response
from billing import HkJingXiuBilling
from billing.keys import CurrencyType, KeyCreate
from sqlmodel import Session, select
from app.core.exceptions import (
    AppNotFoundException,
    CourseNotFoundException,
    OrderNotFoundException,
    PaymentPlanNotFoundException,
    PaymentPlanNotActiveException,
    ProductTypeMismatchException,
    ProductNotInPlanScopeException,
)
from wechatpy import WeChatPay

from app.core import payment_logger, settings
from app.db.models import Agent, Course, Order, PaymentPlan, ProductType

AgentType = Agent.Type


async def get_order_by_id(order_id: str, session: Session) -> Optional[Order]:
    """根据ID获取订单"""
    statement = select(Order).where(Order.id == order_id)
    order = session.exec(statement).first()
    if not order:
        raise OrderNotFoundException()
    return order


def get_alipay_client() -> Optional[AliPay]:
    """
    获取支付宝客户端实例
    """
    try:
        # 读取应用私钥
        private_key_path = (
            Path(__file__).parent.parent.parent / settings.alipay_private_key_path
        )
        payment_logger.debug(f"正在读取应用私钥文件: {private_key_path}")

        if not private_key_path.exists():
            payment_logger.error(f"应用私钥文件不存在: {private_key_path}")
            return None

        with open(private_key_path) as f:
            app_private_key_string = f.read()
            payment_logger.debug("成功读取应用私钥文件内容")

            if not app_private_key_string.strip().startswith("-----BEGIN"):
                payment_logger.error("应用私钥格式不正确，缺少PEM边界")
                return None

        # 读取支付宝公钥
        public_key_path = (
            Path(__file__).parent.parent.parent / settings.alipay_public_key_path
        )
        payment_logger.debug(f"正在读取支付宝公钥文件: {public_key_path}")

        if not public_key_path.exists():
            payment_logger.error(f"支付宝公钥文件不存在: {public_key_path}")
            return None

        with open(public_key_path) as f:
            alipay_public_key_string = f.read()
            payment_logger.debug("成功读取支付宝公钥文件内容")

            if not alipay_public_key_string.strip().startswith("-----BEGIN"):
                payment_logger.error("支付宝公钥格式不正确，缺少PEM边界")
                return None

        payment_logger.debug("正在初始化支付宝客户端实例")
        alipay = AliPay(
            appid=settings.alipay_appid,
            app_notify_url=settings.alipay_notify_url,
            app_private_key_string=app_private_key_string,
            alipay_public_key_string=alipay_public_key_string,
            debug=settings.alipay_debug,
        )
        payment_logger.debug("成功初始化支付宝客户端实例")
        return alipay
    except Exception as e:
        payment_logger.exception(f"初始化支付宝客户端失败: {str(e)}")
        return None


def create_alipay_pc_web_order(
    order_id: str, total_amount: float, subject: str
) -> Optional[str]:
    """
    支付宝下单，适用于PC端网页支付

    参考 [支付宝PC网页支付](https://opendocs.alipay.com/open/59da99d0_alipay.trade.page.pay?scene=22&pathHash=e26b497f)

    Args:
        order_id: 订单号

        total_amount: 支付金额
        subject: 订单标题

    Returns:
        支付链接或None
    """
    try:
        alipay = get_alipay_client()
        if not alipay:
            return None
        order_string = alipay.api_alipay_trade_page_pay(
            out_trade_no=order_id,
            total_amount=str(total_amount),
            subject=subject,
            return_url=settings.alipay_return_url,
            notify_url=settings.alipay_notify_url,
        )

        gateway = (
            "https://openapi-sandbox.dl.alipaydev.com/gateway.do"
            if settings.alipay_debug
            else "https://openapi.alipay.com/gateway.do"
        )
        return f"{gateway}?{order_string}"
    except Exception as e:
        payment_logger.exception(f"创建支付宝订单失败: {str(e)}")
        return None


def create_alipay_wap_pay_order(
    order_id: str, total_amount: float, subject: str
) -> Optional[str]:
    """
    创建支付宝手机网页支付订单

    参考 [支付宝手机网页支付](https://opendocs.alipay.com/open/29ae8cb6_alipay.trade.wap.pay?scene=21&pathHash=1ef587fd)

    Args:
        order_id: 订单号
        total_amount: 支付金额
        subject: 订单标题

    Returns:
        支付链接或None
    """
    try:
        alipay = get_alipay_client()
        if not alipay:
            return None

        # 调用支付宝手机网页支付接口
        order_string = alipay.api_alipay_trade_wap_pay(
            out_trade_no=order_id,
            total_amount=str(total_amount),
            subject=subject,
            return_url=settings.alipay_return_url,
            notify_url=settings.alipay_notify_url,
            product_code="QUICK_WAP_WAY",
            quit_url=settings.alipay_quit_url,
        )

        # 根据调试模式选择网关
        gateway = (
            "https://openapi-sandbox.dl.alipaydev.com/gateway.do"
            if settings.alipay_debug
            else "https://openapi.alipay.com/gateway.do"
        )
        return f"{gateway}?{order_string}"
    except Exception as e:
        payment_logger.exception(f"创建支付宝手机网页支付订单失败: {str(e)}")
        return None


def get_wechat_pay_client() -> Optional[WeChatPay]:
    """
    获取微信支付客户端实例

    参考：https://docs.wechatpy.org/zh-cn/stable/pay.html#wechatpay
    """
    try:
        payment_logger.debug("正在初始化微信支付客户端")

        # 读取商户证书
        mch_cert_path = Path(__file__).parent.parent.parent / settings.wechat_cert_path
        mch_key_path = Path(__file__).parent.parent.parent / settings.wechat_key_path

        payment_logger.debug(f"商户证书路径: {mch_cert_path}")
        payment_logger.debug(f"商户证书私钥路径: {mch_key_path}")

        if not mch_cert_path.exists():
            payment_logger.error(f"商户证书文件不存在: {mch_cert_path}")
            return None

        if not mch_key_path.exists():
            payment_logger.error(f"商户证书私钥文件不存在: {mch_key_path}")
            return None

        wechat_pay = WeChatPay(
            appid=settings.wechat_appid,  # 微信公众号appid
            api_key=settings.wechat_api_key,  # 商户API密钥
            mch_id=settings.wechat_mch_id,  # 商户号
            mch_cert=str(mch_cert_path),
            mch_key=str(mch_key_path),
        )
        payment_logger.debug("成功初始化微信支付客户端")
        return wechat_pay
    except Exception as e:
        payment_logger.exception(f"初始化微信支付客户端失败: {str(e)}")
        return None


def create_wechat_jsapi_order(
    order_id: str, total_amount: float, subject: str, openid: str
) -> Optional[dict]:
    """
    创建微信JSAPI支付订单（用于微信内浏览器支付）

    参考：https://pay.weixin.qq.com/docs/merchant/products/jsapi-payment/introduction.html

    Args:
        order_id: 订单号
        total_amount: 支付金额（元）
        subject: 订单标题
        openid: 用户的openid

    Returns:
        JSAPI支付参数或None
    """
    try:
        wechat_pay = get_wechat_pay_client()
        if not wechat_pay:
            return None

        payment_logger.debug(f"创建微信JSAPI支付订单: {order_id}")
        result = wechat_pay.order.create(
            trade_type="JSAPI",
            out_trade_no=order_id,
            total_fee=int(total_amount * 100),  # 微信支付金额单位为分
            body=subject,
            openid=openid,
        )

        if result["return_code"] == "SUCCESS" and result["result_code"] == "SUCCESS":
            # 生成JSAPI调起支付的参数
            payment_logger.debug("生成JSAPI支付参数")
            pay_params = wechat_pay.jsapi.get_jsapi_params(
                prepay_id=result["prepay_id"]
            )
            return pay_params
        else:
            payment_logger.error(f"创建微信JSAPI支付订单失败: {result}")
            return None
    except Exception as e:
        payment_logger.exception(f"创建微信JSAPI支付订单失败: {str(e)}")
        return None


def create_wechat_h5_order(
    order_id: str, total_amount: float, subject: str
) -> Optional[str]:
    """
    创建微信H5支付订单（用于手机浏览器支付）

    参考：https://pay.weixin.qq.com/docs/merchant/apis/h5-payment/h5-pay.html

    Args:
        order_id: 订单号
        total_amount: 支付金额（元）
        subject: 订单标题

    Returns:
        支付链接或None
    """
    try:
        wechat_pay = get_wechat_pay_client()
        if not wechat_pay:
            return None

        payment_logger.debug(f"创建微信H5支付订单: {order_id}")
        result = wechat_pay.order.create(
            trade_type="MWEB",
            body=subject,
            total_fee=int(total_amount * 100),  # 微信支付金额单位为分
            out_trade_no=order_id,
            notify_url=settings.wechat_notify_url,
        )

        if result["return_code"] == "SUCCESS" and result["result_code"] == "SUCCESS":
            payment_logger.debug("获取H5支付链接成功")
            return result["mweb_url"]
        else:
            payment_logger.error(f"创建微信H5支付订单失败: {result}")
            return None
    except Exception as e:
        payment_logger.exception(f"创建微信H5支付订单失败: {str(e)}")
        return None


def create_wechat_native_order(
    order_id: str, total_amount: float, subject: str
) -> Optional[str]:
    """
    创建微信Native支付订单（用于PC端扫码支付）

    参考：https://pay.weixin.qq.com/docs/merchant/apis/native-payment/native-pay.html

    Args:
        order_id: 订单号
        total_amount: 支付金额（元）
        subject: 订单标题

    Returns:
        二维码链接或None
    """
    try:
        wechat_pay = get_wechat_pay_client()
        if not wechat_pay:
            return None

        payment_logger.debug(f"创建微信Native支付订单: {order_id}")
        result = wechat_pay.order.create(
            trade_type="NATIVE",
            body=subject,
            total_fee=int(total_amount * 100),  # 微信支付金额单位为分
            out_trade_no=order_id,
            notify_url=settings.wechat_notify_url,
        )

        if result["return_code"] == "SUCCESS" and result["result_code"] == "SUCCESS":
            payment_logger.debug("获取Native支付二维码链接成功")
            return result["code_url"]  # 此链接用于生成支付二维码
        else:
            payment_logger.error(f"创建微信Native支付订单失败: {result}")
            return None
    except Exception as e:
        payment_logger.exception(f"创建微信Native支付订单失败: {str(e)}")
        return None


def create_local_order(
    payment_method: Order.PaymentMethod,
    product_type: ProductType,
    product_id: str,
    payment_plan_id: int,
    quantity: int,
    session: Session,
    user_id: int,
) -> Order:
    """创建本地订单"""
    # 查询付费计划
    payment_plan = session.exec(
        select(PaymentPlan).where(PaymentPlan.id == payment_plan_id)
    ).first()

    if not payment_plan:
        raise PaymentPlanNotFoundException(plan_id=payment_plan_id)

    if not payment_plan.is_active:
        raise PaymentPlanNotActiveException(plan_id=payment_plan_id)

    # 验证产品类型与付费计划的适用范围类型是否匹配
    if product_type.value != payment_plan.scope_type.value:
        raise ProductTypeMismatchException()

    # 验证产品ID是否在付费计划的适用范围内
    if payment_plan.scope_ids and product_id not in payment_plan.scope_ids:
        raise ProductNotInPlanScopeException()

    # 根据产品类型验证产品是否存在
    if product_type == ProductType.APP:
        # 查询对应的应用
        app = session.exec(select(Agent).where(Agent.id == product_id)).first()
        if not app:
            raise AppNotFoundException(app_id=product_id)
    elif product_type == ProductType.COURSE:
        # 查询对应的课程
        course = session.exec(select(Course).where(Course.id == product_id)).first()
        if not course:
            raise CourseNotFoundException(course_id=product_id)

    # 计算订单金额
    total_amount = payment_plan.price * quantity

    # 创建订单记录
    new_order = Order(
        id=str(uuid.uuid4()).replace("-", "")[:32],  # 生成32位唯一ID
        amount=total_amount,
        payment_method=payment_method,
        status=Order.Status.PENDING,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        user_id=user_id,
        product_type=product_type,
        product_id=product_id,
        payment_plan_id=payment_plan_id,
        quantity=quantity,
    )

    session.add(new_order)
    session.commit()
    session.refresh(new_order)
    return new_order


async def process_payment_for_order(order: Order, session: Session):
    """处理订单支付后的操作，创建相应的密钥"""
    billing_client = HkJingXiuBilling(
        base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
    )

    # 查询付费计划
    payment_plan = session.exec(
        select(PaymentPlan).where(PaymentPlan.id == order.payment_plan_id)
    ).first()

    if not payment_plan:
        raise PaymentPlanNotFoundException(plan_id=order.payment_plan_id)

    # 处理应用类产品
    if order.product_type == ProductType.APP:
        # 查询应用信息
        app = session.exec(
            select(Agent).where(Agent.id == order.product_id)
        ).first()
        if not app:
            raise AppNotFoundException(app_id=order.product_id)

        # 计算过期时间
        expires_at = (
            (datetime.now() + timedelta(days=payment_plan.validity_period))
            if (payment_plan.validity_period and payment_plan.validity_period > 0)
            else None
        )

        # 检查是否是捆绑包类型
        is_bundle = False
        bundle_agent_ids = []

        # 如果是捆绑包，获取该智能体包含的所有智能体ID
        if app.type == AgentType.BUNDLE:
            is_bundle = True
            # 直接从bundle_agent_ids字段获取捆绑包中包含的智能体ID
            if hasattr(app, 'bundle_agent_ids') and app.bundle_agent_ids:
                bundle_agent_ids = app.bundle_agent_ids
                payment_logger.info(
                    f"捆绑包 {app.id} 包含智能体: {bundle_agent_ids}"
                )
            bundle_agent_ids.append(app.id)

        # 创建密钥
        if is_bundle and bundle_agent_ids:
            # 如果是捆绑包且包含智能体ID，则创建一个作用域为多个智能体ID的密钥
            key = await billing_client.keys.create_key(
                KeyCreate(
                    user_id=str(order.user_id),
                    service_code="AGC",  # 使用非管理密钥的服务代码
                    currency_type=CurrencyType.CNY,  # 使用非管理密钥的货币类型
                    scope=bundle_agent_ids,  # 多个智能体ID的作用域
                    credit_limit=order.amount,
                    expires_at=expires_at,
                )
            )
        else:
            # 如果不是捆绑包，则创建一个作用域为单个智能体ID的密钥
            key = await billing_client.keys.create_key(
                KeyCreate(
                    user_id=str(order.user_id),
                    service_code="AGC",  # 使用非管理密钥的服务代码
                    currency_type=CurrencyType.CNY,  # 使用非管理密钥的货币类型
                    scope=[order.product_id],  # 单个智能体ID的作用域
                    credit_limit=order.amount,
                    expires_at=expires_at,
                )
            )
        payment_logger.info(
            f"为用户 {order.user_id} 创建密钥: {key.id}，过期时间: {expires_at}"
        )

    # 处理课程类产品
    elif order.product_type == ProductType.COURSE:
        # 查询课程信息
        course = session.exec(
            select(Course).where(Course.id == order.product_id)
        ).first()
        if not course:
            payment_logger.error(f"课程不存在: {order.product_id}")
            return

        # 计算过期时间（如果付费计划有有效期）
        expires_at = None
        if payment_plan.validity_period and payment_plan.validity_period > 0:
            expires_at = datetime.now() + timedelta(days=payment_plan.validity_period)
            payment_logger.info(
                f"课程 {course.id} 的访问权限将于 {expires_at} 过期"
            )

        # 创建密钥，与智能体类似，但使用COU服务代码
        key = await billing_client.keys.create_key(
            KeyCreate(
                user_id=str(order.user_id),
                service_code="COU",  # 使用COU服务代码表示课程
                currency_type=CurrencyType.CNY,
                scope=[str(course.id)],  # 课程ID的作用域
                credit_limit=order.amount,
                expires_at=expires_at,
            )
        )

        payment_logger.info(
            f"用户 {order.user_id} 购买了课程 {course.id}: {course.title}，创建密钥: {key.id}，过期时间: {expires_at if expires_at else '永久'}"
        )

    session.commit()


def wechat_response(return_code: str, return_msg: str) -> Response:
    """将微信异步通知的响应包装为XML格式"""
    if return_msg is None:
        return_msg = ""
    xml = f'<xml><return_code><![CDATA[{return_code}]]></return_code><return_msg><![CDATA[{return_msg}]]></return_msg></xml>'
    return Response(content=xml, media_type='application/xml')
