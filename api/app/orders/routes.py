from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlmodel import Session

from app.core import ResponsePayloads, payment_logger, settings
from app.db import get_session
from app.db.models import Order
from app.users.api.utils import get_current_user

from .schemas import OrderCreateRequest, OrderResult
from .utils import (
    create_alipay_pc_web_order,
    create_alipay_wap_pay_order,
    create_local_order,
    create_wechat_h5_order,
    create_wechat_jsapi_order,
    create_wechat_native_order,
    get_alipay_client,
    get_order_by_id,
    get_wechat_pay_client,
    process_assets_for_order 
)

router = APIRouter(prefix="/orders", tags=["订单"])


@router.post("/", summary="创建订单", response_model=ResponsePayloads[OrderResult])
async def create_order(
    data: OrderCreateRequest,
    request: Request,
    current_user=Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """创建订单并生成支付链接"""
    try:
        # 创建本地订单
        local_order = create_local_order(
            payment_method=data.payment_method,
            items=data.items,
            session=session,
            user_id=current_user.id,
        )

        # 创建支付宝订单
        if data.payment_method == Order.PaymentMethod.ALIPAY:
            # 根据设备类型选择支付方式
            user_agent = request.headers.get("User-Agent", "").lower()
            if "mobile" in user_agent:
                payment_url = create_alipay_wap_pay_order(
                    order_id=local_order.id,
                    amount=local_order.amount,
                    subject=f"订单 #{local_order.id}",
                )
            else:
                payment_url = create_alipay_pc_web_order(
                    order_id=local_order.id,
                    total_amount=local_order.amount,
                    subject=f"订单 #{local_order.id}",
                )

            if not payment_url:
                raise HTTPException(status_code=500, detail="创建支付订单失败")
        elif data.payment_method == Order.PaymentMethod.WECHATPAY:
            # 微信支付处理
            user_agent = request.headers.get("User-Agent", "").lower()
            if "micromessenger" in user_agent:  # 微信浏览器
                openid = request.headers.get("X-WX-OPENID")  # 需要前端传递用户openid
                if not openid:
                    raise HTTPException(status_code=400, detail="缺少openid参数")
                payment_params = create_wechat_jsapi_order(
                    order_id=local_order.id,
                    total_amount=local_order.amount,
                    subject=f"订单 #{local_order.id}",
                    openid=openid,
                )
                if not payment_params:
                    raise HTTPException(status_code=500, detail="创建支付订单失败")
                return ResponsePayloads(
                    data=OrderResult(
                        order_id=local_order.id,
                        payment_params=payment_params,
                        message="订单创建成功",
                    )
                )
            elif "mobile" in user_agent:  # 手机浏览器
                payment_url = create_wechat_h5_order(
                    order_id=local_order.id,
                    total_amount=local_order.amount,
                    subject=f"订单 #{local_order.id}",
                )
            else:  # PC端
                payment_url = create_wechat_native_order(
                    order_id=local_order.id,
                    total_amount=local_order.amount,
                    subject=f"订单 #{local_order.id}",
                )
        else:
            raise HTTPException(status_code=400, detail="不支持的支付方式")

        if not payment_url:
            raise HTTPException(status_code=500, detail="创建支付订单失败")

        return ResponsePayloads(
            data=OrderResult(
                order_id=local_order.id, payment_url=payment_url, message="订单创建成功"
            )
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/detail", summary="获取订单详情", response_model=ResponsePayloads[Order])
async def get_order(
    order_id: str,
    session: Session = Depends(get_session),
):
    """获取订单详情"""
    try:
        order = await get_order_by_id(order_id, session)
        return ResponsePayloads(data=order)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单详情失败: {str(e)}")


@router.post("/alipay/notify", summary="支付宝异步通知")
async def alipay_notify(request: Request, session: Session = Depends(get_session)):
    """处理支付宝异步通知

    参考文档：https://opendocs.alipay.com/open/203/105286
    """
    try:
        # 获取通知数据
        data = await request.form()
        payment_logger.info(f"收到支付宝异步通知: {data}")

        # 获取支付宝客户端
        alipay_client = get_alipay_client()
        if not alipay_client:
            payment_logger.error("获取支付宝客户端失败")
            return "fail"

        # 将请求参数转换为字典并过滤空值
        data_dict = {
            k: v for k, v in data.items() if v and k not in ["sign", "sign_type"]
        }
        signature = data.get("sign")

        # 增加调试日志
        payment_logger.debug(f"待验证参数: {data_dict}")
        payment_logger.debug(f"收到签名: {signature}")

        # 验证签名（增加字符集处理）
        success = alipay_client.verify(data_dict, signature)

        if not success:
            # 增加调试信息
            payment_logger.error(
                f"签名验证失败，待签名字符串: {alipay_client._build_sign_string(data_dict)}"
            )
            payment_logger.error(f"本地公钥: {alipay_client.alipay_public_key_string}")
            return "fail"

        # 验证通知中的 app_id 是否为该商户本身
        if data.get("app_id") != settings.alipay_appid:
            payment_logger.error(f"app_id不匹配: {data.get('app_id')}")
            return "fail"

        # 获取订单信息
        order_id = data.get("out_trade_no")
        trade_status = data.get("trade_status")

        if not order_id or not trade_status:
            payment_logger.error("缺少订单号或交易状态")
            return "fail"

        # 在验证订单金额处添加日志
        payment_logger.debug(f"支付宝通知订单号: {order_id} (长度: {len(order_id)})")

        # 处理不同的交易状态
        if trade_status in ["TRADE_SUCCESS", "TRADE_FINISHED"]:
            try:
                # 查询订单
                order = await get_order_by_id(order_id, session)
                if not order:
                    payment_logger.error(f"订单不存在: {order_id}")
                    return "fail"
                # 已经支付的订单不重复处理
                if order.status == Order.Status.PAID:
                    payment_logger.info(f"订单 {order_id} 已经支付，不重复处理")
                    return "success"
                # 验证订单金额
                notify_amount = float(data.get("total_amount", 0))
                if notify_amount != order.amount:
                    payment_logger.error(
                        f"订单金额不匹配: 通知金额={notify_amount}, 订单金额={order.amount}"
                    )
                    return "fail"

                # 更新订单状态
                order.status = Order.Status.PAID
                order.updated_at = datetime.now()
                order.pay_time = datetime.now()
                session.add(order)

                # 处理资产（调用工具函数）
                await process_assets_for_order(order, session)
                session.commit()
                payment_logger.info(f"订单 {order_id} 支付成功")
                return "success"
            except Exception as e:
                payment_logger.exception(f"处理订单 {order_id} 失败: {str(e)}")
                session.rollback()
                return "fail"
        else:
            payment_logger.info(
                f"订单 {order_id} 的交易状态为 {trade_status}，不做处理"
            )
            return "success"

    except Exception as e:
        payment_logger.exception(f"处理支付宝异步通知失败: {str(e)}")
        return "fail"


@router.post("/wechat/notify", summary="微信支付异步通知")
async def wechat_notify(request: Request, session: Session = Depends(get_session)):
    """处理微信支付异步通知

    参考文档：https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_7
    """
    try:
        # 获取原始XML数据
        xml_data = await request.body()
        payment_logger.info(f"收到微信支付异步通知: {xml_data}")

        # 获取微信支付客户端
        wechat_pay = get_wechat_pay_client()
        if not wechat_pay:
            payment_logger.error("获取微信支付客户端失败")
            return {"code": "FAIL", "message": "签名验证失败"}

        try:
            # 解析并验证签名
            data = wechat_pay.parse_payment_result(xml_data)
            payment_logger.debug(f"解析后的通知数据: {data}")
        except Exception as e:
            payment_logger.error(f"解析微信支付通知失败: {str(e)}")
            return {"code": "FAIL", "message": "签名验证失败"}

        # 验证支付结果
        if data["return_code"] != "SUCCESS":
            payment_logger.error(f"通信标识失败: {data.get('return_msg')}")
            return {"code": "FAIL", "message": data.get("return_msg")}

        if data["result_code"] != "SUCCESS":
            payment_logger.error(f"业务结果失败: {data.get('err_code_des')}")
            return {"code": "FAIL", "message": data.get("err_code_des")}

        # 获取订单信息
        order_id = data.get("out_trade_no")
        if not order_id:
            payment_logger.error("缺少订单号")
            return {"code": "FAIL", "message": "缺少订单号"}

        # 在验证订单金额处添加日志
        payment_logger.debug(f"微信通知订单号: {order_id} (长度: {len(order_id)})")

        try:
            # 查询订单
            order = await get_order_by_id(order_id, session)
            if not order:
                payment_logger.error(f"订单不存在: {order_id}")
                return {"code": "FAIL", "message": "订单不存在"}
            # 已经支付的订单不重复处理
            if order.status == Order.Status.PAID:
                payment_logger.info(f"订单 {order_id} 已经支付，不重复处理")
                return {"code": "SUCCESS", "message": "OK"}
            # 验证订单金额（微信支付金额单位为分）
            notify_amount = float(data.get("total_fee", 0)) / 100
            if notify_amount != order.amount:
                payment_logger.error(
                    f"订单金额不匹配: 通知金额={notify_amount}, 订单金额={order.amount}"
                )
                return {"code": "FAIL", "message": "订单金额不匹配"}

            # 验证商户ID
            if data.get("mch_id") != settings.wechat_mch_id:
                payment_logger.error(f"商户号不匹配: {data.get('mch_id')}")
                return {"code": "FAIL", "message": "商户号不匹配"}

            # 更新订单状态
            order.status = Order.Status.PAID
            order.updated_at = datetime.now()
            order.pay_time = datetime.now()
            session.add(order)

            # 处理订单资产
            process_assets_for_order(order, session) 
            session.commit()
            payment_logger.info(f"订单 {order_id} 支付成功")
            return {"code": "SUCCESS", "message": "OK"}

        except Exception as e:
            payment_logger.exception(f"处理订单 {order_id} 失败: {str(e)}")
            session.rollback()
            return {"code": "FAIL", "message": "处理订单失败"}

    except Exception as e:
        payment_logger.exception(f"处理微信支付通知失败: {str(e)}")
        return {"code": "FAIL", "message": "处理失败"}
