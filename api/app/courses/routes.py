from typing import Dict, List, Optional, Set

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from loguru import logger
from sqlmodel import Session, select

from app.core import Pagination, ResponsePayloads, redis_client, settings
from app.db import get_session
from app.db.models import Agent, Course, CourseSection, User
from app.users.api import get_current_user
from billing import HkJingXiuBilling

from .schemas import (
    CourseDetail,
    CourseOutline,
    CourseSectionOutline,
    CoursesByTag,
    RatingDistribution,
    CourseStats,
)
from .utils import check_user_has_purchased_course

router = APIRouter(prefix="/courses", tags=["课程"])
AgentType = Agent.Type


@router.get(
    "/search",
    summary="搜索课程",
    response_model=ResponsePayloads[Pagination[CourseOutline]],
)
async def search_courses(
    teacher_id: Optional[int] = Query(None, description="导师ID"),
    instructor: Optional[str] = Query(None, description="讲师名称"),
    keyword: Optional[str] = Query(None, description="搜索关键词（标题/描述）"),
    tags: Optional[List[str]] = Query(None, description="标签过滤列表"),
    exclude_ids: List[str] = Query(None, description="排除的课程ID列表"),
    page: int = Query(1, description="页码，从1开始"),
    page_size: int = Query(10, description="每页数量"),
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """搜索课程列表（支持讲师名称、关键词、标签组合查询）"""
    statement = select(Course)

    # 添加过滤条件
    if instructor:
        statement = statement.where(Course.instructor == instructor)
    elif teacher_id:
        # 为了向后兼容，保留teacher_id参数的处理
        statement = statement.where(Course.instructor == str(teacher_id))

    # 关键词搜索条件（标题或描述包含关键词）
    if keyword:
        search_keyword = f"%{keyword}%"
        statement = statement.where(
            (Course.title.ilike(search_keyword))
            | (Course.description.ilike(search_keyword))
        )

    # 标签过滤条件
    if tags:
        # 过滤掉空标签和"全部"标签
        valid_tags = [tag for tag in tags if tag and tag.strip() and tag != "全部"]
        if valid_tags:
            statement = statement.where(Course.tags.contains(valid_tags))

    # 排除特定ID的课程
    if exclude_ids:
        # 将字符串ID转换为整数ID
        try:
            int_exclude_ids = [
                int(id_str) for id_str in exclude_ids if id_str.isdigit()
            ]
            if int_exclude_ids:
                statement = statement.where(Course.id.not_in(int_exclude_ids))
        except ValueError as e:
            logger.warning(f"排除ID列表转换失败: {e}")

    # 添加排序（可以根据需要添加适当的排序字段）
    statement = statement.order_by(Course.created_at.desc())

    # 计算总数
    total = len(session.exec(statement).all())

    # 添加分页
    statement = statement.offset((page - 1) * page_size).limit(page_size)

    # 执行查询
    courses = session.exec(statement).all()

    # 获取课程章节数量的函数
    def get_sections_count(course_id: int):
        return session.exec(
            select(CourseSection).where(CourseSection.course_id == course_id)
        ).all()

    # 构建课程概要列表
    course_outlines = [
        CourseOutline(
            id=course.id,
            title=course.title,
            name=course.title,
            description=course.description,
            icon=course.cover_image,
            cover_image=course.cover_image,
            poster_url=course.poster_url,
            tags=course.tags,
            instructor=course.instructor,
            sections_count=len(get_sections_count(course.id)),
        )
        for course in courses
    ]

    # 构造分页响应
    return ResponsePayloads(
        data=Pagination(
            data=course_outlines,
            total=total,
            has_more=(page * page_size) < total,
        )
    )


@router.get(
    "/tags",
    summary="获取所有课程标签",
    response_model=ResponsePayloads[List[str]],
)
async def get_all_course_tags(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """获取所有课程的标签列表"""
    try:
        # 查询所有课程
        courses = session.exec(select(Course)).all()

        # 收集所有标签并去重
        all_tags: Set[str] = set()
        for course in courses:
            if course.tags:
                all_tags.update(course.tags)

        return ResponsePayloads(data=list(all_tags))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/by_tags",
    summary="获取按标签分组的课程列表",
    response_model=ResponsePayloads[CoursesByTag],
    description="获取按标签分组的课程列表，返回格式为 {标签1: [课程1, 课程2], 标签2: [课程3, 课程4]}",
)
async def get_courses_by_tags(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """获取按标签分组的课程列表

    特殊规则：
    1. "精选推荐"标签默认返回4个课程
    2. 其它标签默认返回6个课程
    3. 没有标签的课程归类到"其他"标签中

    性能优化：
    1. 使用缓存减少数据库查询
    2. 针对每个标签单独查询，限制数量
    3. 使用数据库排序而非内存排序
    """
    cache_key = f"courses_by_tags"



    # 获取所有标签
    all_tags_query = select(Course.tags)
    all_tags_result = session.exec(all_tags_query).all()

    # 提取所有唯一标签
    unique_tags = set()
    for tags_list in all_tags_result:
        if tags_list:
            unique_tags.update(tags_list)

    result: Dict[str, List[CourseOutline]] = {}

    # 处理"精选推荐"标签 - 限制为4个
    if "精选推荐" in unique_tags:
        recommended_query = (
            select(Course).where(Course.tags.contains(["精选推荐"])).limit(4)
        )

        recommended_courses = session.exec(recommended_query).all()
        result["精选推荐"] = []

        for course in recommended_courses:
            # 获取课程章节数量
            sections_count = len(
                session.exec(
                    select(CourseSection).where(CourseSection.course_id == course.id)
                ).all()
            )

            result["精选推荐"].append(
                CourseOutline(
                    id=course.id,
                    title=course.title,
                    name=course.title,
                    description=course.description,
                    icon=course.cover_image,
                    poster_url=course.poster_url,
                    tags=course.tags,
                    instructor=course.instructor,
                    sections_count=sections_count,
                )
            )

        # 从唯一标签中移除已处理的标签
        unique_tags.remove("精选推荐")

    # 处理其他标签 - 每个限制为6个
    for tag in unique_tags:
        tag_query = select(Course).where(Course.tags.contains([tag])).limit(6)

        tag_courses = session.exec(tag_query).all()
        if tag_courses:
            result[tag] = []

            for course in tag_courses:
                # 获取课程章节数量
                sections_count = len(
                    session.exec(
                        select(CourseSection).where(
                            CourseSection.course_id == course.id
                        )
                    ).all()
                )

                result[tag].append(
                    CourseOutline(
                        id=course.id,
                        title=course.title,
                        name=course.title,
                        description=course.description,
                        icon=course.cover_image,
                        cover_image=course.cover_image,
                        poster_url=course.poster_url,
                        tags=course.tags,
                        instructor=course.instructor,
                        sections_count=sections_count,
                    )
                )

    # 处理无标签课程 - 归类到"其他"标签，限制为6个
    no_tag_query = select(Course).where(Course.tags == []).limit(6)

    no_tag_courses = session.exec(no_tag_query).all()
    if no_tag_courses:
        if "其他" not in result:
            result["其他"] = []

        for course in no_tag_courses:
            # 获取课程章节数量
            sections_count = len(
                session.exec(
                    select(CourseSection).where(CourseSection.course_id == course.id)
                ).all()
            )

            result["其他"].append(
                CourseOutline(
                    id=course.id,
                    title=course.title,
                    cover_image=course.cover_image,
                    poster_url=course.poster_url,
                    tags=course.tags,
                    price=course.price,
                    instructor=course.instructor,
                    sections_count=sections_count,
                )
            )

    # 创建结果对象
    courses_by_tag_result = CoursesByTag(data=result)

    # 更新缓存
    try:
        # 使用Pydantic的model_dump_json方法序列化为JSON
        json_data = courses_by_tag_result.model_dump_json()
        # 设置缓存，带过期时间
        redis_client.set(cache_key, json_data, ex=300)
    except Exception as e:
        logger.error(f"缓存序列化失败: {str(e)}")

    return ResponsePayloads(data=courses_by_tag_result)


@router.get(
    "/details/{course_id}",
    summary="获取课程详情",
    response_model=ResponsePayloads[CourseDetail],
)
async def get_course_detail(
    course_id: int = Path(..., description="课程ID"),
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """根据课程ID获取课程详细信息"""
    # 缓存键名
    cache_hash_key = "course_details"

    # 尝试从缓存获取
    try:
        cached_json = redis_client.hget(cache_hash_key, str(course_id))
        if cached_json:
            # 使用Pydantic的model_validate_json方法反序列化JSON
            cached_result = CourseDetail.model_validate_json(cached_json)
            logger.debug(f"从缓存获取课程详情: {course_id}")

            # 检查用户是否已购买该课程（不参与缓存）
            cached_result.has_purchased = await check_user_has_purchased_course(
                current_user, course_id, session
            )

            return ResponsePayloads(data=cached_result)
    except Exception as e:
        logger.error(f"从缓存获取课程详情失败: {str(e)}")

    # 缓存未命中，从数据库获取
    course = session.get(Course, course_id)
    if not course:
        raise HTTPException(status_code=404, detail="课程不存在")

    sections = session.exec(
        select(CourseSection)
        .where(CourseSection.course_id == course_id)
        .order_by(CourseSection.id.asc())
    ).all()

    # 检查用户是否已购买该课程
    has_purchased = await check_user_has_purchased_course(
        current_user, course.id, session
    )

    # 创建课程详情对象
    course_detail = CourseDetail(
        id=course.id,
        title=course.title,
        name=course.title,
        cover_image=course.cover_image,
        poster_url=course.poster_url,
        description=course.description,
        tags=course.tags,
        instructor=course.instructor,
        sections=[
            CourseSectionOutline(
                id=section.id,
                title=section.title,
                duration=section.duration,
                is_free=section.is_free,
                video_url=section.video_url,
            )
            for section in sections
        ],
        has_purchased=has_purchased,  # 设置用户是否已购买
    )

    # 生成模拟的统计数据 - 使用随机数
    import random

    # 生成随机评分 (3.5-4.9之间)
    rating = round(random.uniform(3.5, 4.9), 1)

    # 生成随机的评级分布数据 (总和为100%)
    five_star = round(random.uniform(50, 75), 2)
    four_star = round(random.uniform(10, 25), 2)
    three_star = round(random.uniform(5, 15), 2)
    two_star = round(random.uniform(2, 8), 2)

    # 计算一星评分，确保非负
    one_star = max(0, round(100 - five_star - four_star - three_star - two_star, 2))

    # 如果总和不等于100%，调整五星评分使总和为100%
    total = five_star + four_star + three_star + two_star + one_star
    if total != 100:
        five_star = round(five_star + (100 - total), 2)

    rating_distribution = [
        RatingDistribution(stars=5, percentage=five_star),
        RatingDistribution(stars=4, percentage=four_star),
        RatingDistribution(stars=3, percentage=three_star),
        RatingDistribution(stars=2, percentage=two_star),
        RatingDistribution(stars=1, percentage=one_star),
    ]

    # 随机生成评级数量
    rating_counts = ["10K+", "25K+", "50K+", "100K+", "250K+", "500K+"]
    rating_count = random.choice(rating_counts)

    # 随机生成排名
    rank = f"#{random.randint(1, 50)}"

    # 随机生成学生数量
    student_counts = ["500K+", "1M+", "2M+", "3M+", "5M+", "10M+"]
    student_count = random.choice(student_counts)

    # 获取课程的第一个标签作为类别，如果没有标签则使用"其他"
    category = course.tags[0] if course.tags and len(course.tags) > 0 else "其他"

    # 创建统计信息
    course_detail.stats = CourseStats(
        rating=rating,
        rating_count=rating_count,
        rank=rank,
        category=category,
        student_count=student_count,
        rating_distribution=rating_distribution,
    )

    # 更新缓存
    try:
        # 使用Pydantic的model_dump_json方法序列化为JSON
        json_data = course_detail.model_dump_json()
        # 设置缓存，使用哈希结构存储
        redis_client.hset(cache_hash_key, str(course_id), json_data)
        # 设置整个哈希的过期时间（如果不存在）
        if not redis_client.ttl(cache_hash_key) > 0:
            redis_client.expire(cache_hash_key, 300)  # 5分钟过期
        logger.debug(f"更新课程详情缓存: {course_id}")
    except Exception as e:
        logger.error(f"缓存课程详情失败: {str(e)}")

    return ResponsePayloads(data=course_detail)

@router.get(
    "/owned",
    summary="获取用户拥有的课程列表",
    response_model=ResponsePayloads[List[CourseOutline]],
    description="获取当前用户通过密钥拥有权限的课程列表",
)
async def get_owned_courses(
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """获取用户拥有的课程列表

    此接口通过查询用户在商户平台上拥有的密钥，获取用户有权限使用的课程列表。

    返回:
        List[CourseOutline]: 用户拥有的课程列表
    """
    try:
        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 获取用户所有可用的课程密钥
        keys = await billing_client.keys.list_available_keys(
            user_id=str(current_user.id),
            service_code="COU",  # 使用COU服务代码表示课程
        )

        # 提取所有密钥的scope中的课程ID
        course_ids = set()
        for key in keys.keys:
            if key.scope:
                # 将字符串ID转换为整数
                course_ids.update([int(course_id) for course_id in key.scope])

        # 如果没有找到任何课程ID，返回空列表
        if not course_ids:
            return ResponsePayloads(data=[])

        # 查询这些课程的详细信息
        courses = []
        for course_id in course_ids:
            course = session.exec(select(Course).where(Course.id == course_id)).first()
            if course:
                # 获取课程章节数量
                sections_count = len(
                    session.exec(
                        select(CourseSection).where(CourseSection.course_id == course.id)
                    ).all()
                )

                courses.append(
                    CourseOutline(
                        id=course.id,
                        title=course.title,
                        name=course.title,
                        description=course.description,
                        icon=course.cover_image,
                        cover_image=course.cover_image,
                        poster_url=course.poster_url,
                        tags=course.tags,
                        instructor=course.instructor,
                        sections_count=sections_count,
                    )
                )

        return ResponsePayloads(data=courses)
    except Exception as e:
        logger.exception(f"获取用户拥有的课程失败: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"获取用户拥有的课程失败: {str(e)}"
        )
