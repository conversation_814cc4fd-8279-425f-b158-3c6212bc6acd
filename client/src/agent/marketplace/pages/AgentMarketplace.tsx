import React, { useState, useRef, useEffect } from 'react';
import { Container, Space, Stack, Group, Box, useMantineTheme, Text, Skeleton, Button } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { useDeviceDetect, useScrollSpy } from '~/core/hooks';
import { ModuleTitle, SearchBox, FilterTabs, CategoryTitle, ModuleList, ModuleCard, LoadMore } from '~/core/components';
import { useAgentMarketplace, useAgentDetail } from '../hooks';
import { Agent } from '../schemas';
import { AgentDetailModal } from '../components';
import { RiRocketLine } from 'react-icons/ri';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUserStore } from '~/user/core/store';

/**
 * AgentMarketplace组件 - 展示AI智能体市场页面
 *
 * 该组件实现了一个分类展示的智能体市场，包含以下功能：
 * 1. 顶部搜索框用于搜索智能体
 * 2. 分类标签栏用于快速导航到不同类别
 * 3. 按类别分组展示智能体卡片
 * 4. 支持滚动时自动更新当前活动分类
 * 5. 响应式布局适配移动端和桌面端
 * 6. 加载更多功能用于分页加载内容
 */
const AgentMarketplace: React.FC = () => {
  // 获取Mantine主题和颜色方案
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 检测设备类型，用于响应式布局
  const { isMobile } = useDeviceDetect();

  // 根据当前主题设置粘性导航栏背景色
  const stickyBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const { userInfo, token, setUserInfo } = useUserStore();
  const location = useLocation();

  // 根据路由确定智能体类型
  const getAgentTypes = () => {
    if (location.pathname.startsWith('/workflow')) {
      return ['WORKFLOW'];
    } else if (location.pathname.startsWith('/agent')) {
      return ['CHAT', 'AGENT_CHAT', 'COMPLETION', 'BUNDLE'];
    }
    return undefined;
  };

  // 使用智能体商店钩子获取数据和状态
  const {
    tags,
    agents,
    isAgentsLoading,
    agentsError,
    handleCategoryChange: storeHandleCategoryChange,
    searchKeyword,
    searchResult,
    isSearchLoading,
    searchError,
    research,
    setSearchKeyword,
    loadMore,
    loadingCategories,
  } = useAgentMarketplace(getAgentTypes());

  // 将标签转换为分类数据格式
  const categories = tags.map((tag) => ({
    title: tag,
    subtitle: `${tag}分类下的智能体`,
  }));

  // 如果没有分类数据，添加默认分类
  if (categories.length === 0) {
    categories.push({
      title: '精选推荐',
      subtitle: 'AI资讯‌是指关于人工智能的最新动态、研究成果、技术进展和应用案例等信息',
    });
  }

  // 组件状态管理
  const [loadMoreDisabledMap, setLoadMoreDisabledMap] = useState<Record<string, boolean>>({}); // 每个分类的加载更多禁用状态
  const [tagAgentsMap, setTagAgentsMap] = useState<Record<string, Agent[]>>({}); // 每个分类的智能体列表（包括加载更多的）

  // 初始化数据
  useEffect(() => {
    if (agents && agents.data) {
      // 初始化每个标签的禁用状态和智能体列表
      const initialDisabledMap: Record<string, boolean> = {};
      const initialAgentsMap: Record<string, Agent[]> = {};

      Object.keys(agents.data).forEach((tag) => {
        initialDisabledMap[tag] = false;
        initialAgentsMap[tag] = [...agents.data[tag]];
      });

      setLoadMoreDisabledMap(initialDisabledMap);
      setTagAgentsMap(initialAgentsMap);
    }
  }, [agents]);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null); // 容器引用，用于滚动计算

  // 使用滚动监听钩子
  const { activeSection, selectedCategory, handleCategoryChange } = useScrollSpy({
    containerRef,
    items: categories,
    sectionAttribute: 'data-section',
    sectionIdPrefix: 'section-',
    stickyHeaderSelector: '.sticky',
    triggerOffset: 42,
    manualScrollTimeout: 1000,
  });

  // 当滚动监听钩子的分类变化时，同步到store
  useEffect(() => {
    if (selectedCategory) {
      storeHandleCategoryChange(selectedCategory.title);
    }
  }, [selectedCategory, storeHandleCategoryChange]);

  // 计算当前活动分类（基于滚动位置或用户选择）
  const activeCategory = categories.find((item) => item.title === activeSection) || selectedCategory;

  // 使用智能体详情钩子
  const { isModalOpen, selectedAgentId, handleOpenModal, handleCloseModal } = useAgentDetail();

  // 导航钩子
  const navigate = useNavigate();

  // 根据路由确定页面标题
  const getPageTitle = () => {
    if (location.pathname.startsWith('/workflow')) {
      return {
        title: '工作流商店',
        subtitle: '探索各种功能强大的AI工作流，自动化您的业务流程'
      };
    } else {
      return {
        title: '智能体商店',
        subtitle: '探索各种功能强大的AI智能体，提升您的工作效率和创造力'
      };
    }
  };

  const pageTitle = getPageTitle();

  return (
    <Container size="720px" pt={80} ref={containerRef}>
      {/* 页面标题 */}
      <ModuleTitle title={pageTitle.title} subtitle={pageTitle.subtitle} />

      {/* 创建智能体按钮 - 只在智能体市场页面显示 */}
      {
        userInfo?.can_create_agent && !location.pathname.startsWith('/workflow') && (
          <Group justify="center" mt={32} mb={32}>
            <Button
              variant="gradient"
              gradient={{ from: 'rgba(73, 81, 235, 1)', to: 'rgba(36, 162, 254, 1)', deg: 90 }}
              size={isMobile ? 'md' : 'lg'}
              radius="xl"
              leftSection={<RiRocketLine size={isMobile ? 18 : 20} />}
              onClick={() => navigate('/agent/builder')}
              styles={{
                root: {
                  height: isMobile ? '48px' : '52px',
                  paddingLeft: isMobile ? '24px' : '32px',
                  paddingRight: isMobile ? '24px' : '32px',
                  fontSize: isMobile ? '15px' : '16px',
                  fontWeight: 600,
                  boxShadow: isDark ? '0px 4px 20px rgba(73, 81, 235, 0.35)' : '0px 4px 16px rgba(73, 81, 235, 0.2)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  border: 'none',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: isDark ? '0px 8px 28px rgba(73, 81, 235, 0.45)' : '0px 8px 24px rgba(73, 81, 235, 0.3)',
                  },
                  '&:active': {
                    transform: 'translateY(-1px)',
                    boxShadow: isDark ? '0px 4px 20px rgba(73, 81, 235, 0.35)' : '0px 4px 16px rgba(73, 81, 235, 0.2)',
                  },
                },
              }}
            >
              创建我的专属智能体
            </Button>
          </Group>
        )
      }

      {/* 粘性导航区域：包含搜索框和分类标签 */}
      <Box className="sticky top-0 z-100" pt={16} bg={stickyBgColor}>
        {/* 搜索框 */}
        <SearchBox
          onSearch={research}
          searchKeyword={searchKeyword}
          isSearchLoading={isSearchLoading}
          searchError={searchError}
          searchResult={searchResult}
          setSearchKeyword={setSearchKeyword}
          placeholder="搜索智能体..."
          onItemClick={(asset) => handleOpenModal(asset.id)}
        />

        <Space h={40} />

        {/* 分类标签栏 */}
        <FilterTabs categories={categories} selectedCategory={activeCategory} onCategoryChange={handleCategoryChange} />

        {/* 加载状态提示 - 当加载智能体数据时，分类也在加载中 */}
        {isAgentsLoading && (
          <Text c="dimmed" size="sm" ta="center" mt={10}>
            正在加载分类...
          </Text>
        )}
      </Box>

      {/* 加载状态显示 */}
      {isAgentsLoading && (
        <Stack gap="md" align="center" justify="center" py={80}>
          <Skeleton height={40} width="80%" radius="md" />
          <Skeleton height={200} width="100%" radius="md" />
          <Skeleton height={200} width="100%" radius="md" />
        </Stack>
      )}

      {/* 错误状态显示 */}
      {agentsError && (
        <Stack gap="md" align="center" justify="center" py={80}>
          <Text c="red" size="lg" ta="center">
            加载智能体数据失败: {(agentsError as Error).message}
          </Text>
        </Stack>
      )}

      {/* 没有数据显示 */}
      {!isAgentsLoading && !agentsError && (!agents || Object.keys(agents.data || {}).length === 0) && (
        <Stack gap="md" align="center" justify="center" py={80}>
          <Text c="dimmed" size="lg" ta="center">
            暂无智能体数据
          </Text>
        </Stack>
      )}

      {/* 动态生成分类区块 */}
      {!isAgentsLoading &&
        !agentsError &&
        agents &&
        agents.data &&
        Object.keys(agents.data).map((tag) => (
          <Stack gap={0} py={48} id={`section-${tag}`} data-section={tag} key={tag}>
            <CategoryTitle title={tag} subtitle={`${tag}分类下的智能体`} />

            <Space h={16} />

            {/* 智能体卡片列表 */}
            <ModuleList columns={isMobile ? 1 : 2} gap="md">
              {(tagAgentsMap[tag] || agents.data[tag] || []).map((agent, index) => (
                <ModuleCard
                  key={agent.id}
                  id={agent.id}
                  index={index + 1}
                  name={agent.name}
                  description={agent.description ?? agent.name}
                  iconUrl={agent.icon || 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743067298-ai.jpg'}
                  emphasized={tag === '精选推荐'}
                  onClick={() => handleOpenModal(agent.id)}
                />
              ))}
            </ModuleList>

            {/* 加载更多按钮 */}
            {tagAgentsMap[tag]?.length > 0 && (
              <Group align="center" justify="center" mt={40}>
                <LoadMore
                  text={loadMoreDisabledMap[tag] ? '没有更多内容' : '查看更多'}
                  onClick={async () => {
                    try {
                      // 获取当前分类已加载的智能体ID列表
                      const existingIds = tagAgentsMap[tag].map((agent) => agent.id);

                      // 调用 loadMore 函数获取更多数据
                      const newAgents = await loadMore(tag, existingIds);

                      // 只有在没有更多数据时才禁用加载更多按钮
                      if (newAgents.length < 4) {
                        setLoadMoreDisabledMap((prev) => ({ ...prev, [tag]: true }));
                      }

                      // 更新当前分类的智能体列表
                      setTagAgentsMap((prev) => ({
                        ...prev,
                        [tag]: [...prev[tag], ...newAgents],
                      }));
                    } catch (error) {
                      console.error(`加载更多智能体失败: ${error}`);
                    }
                  }}
                  loading={loadingCategories[tag] || false}
                  disabled={loadMoreDisabledMap[tag] || false}
                />
              </Group>
            )}
          </Stack>
        ))}
      {/* 智能体详情模态框 */}
      <AgentDetailModal opened={isModalOpen} onClose={handleCloseModal} agentId={selectedAgentId} />
    </Container>
  );
};

export default AgentMarketplace;
AgentMarketplace.displayName = 'AgentMarketplace';
