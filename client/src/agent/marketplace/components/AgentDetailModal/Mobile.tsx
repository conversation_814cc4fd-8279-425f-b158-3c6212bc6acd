import React from 'react';
import { Modal, Image } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getDetail } from '../../api';
import { AgentDetail } from '../../schemas';
import { AgentStats, AgentConversationStarters, AgentFeatures, AgentRatings, RelatedAgents } from '../../components';
import { useAgentPayment } from '../../hooks';
import { FaAlipay, FaWeixin } from 'react-icons/fa';
import { PaymentPlanList } from '~/paymentPlan/components';
import './styles.css';

interface MobileAgentDetailModalProps {
  /**
   * 是否打开弹框
   */
  opened: boolean;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
  /**
   * 智能体ID
   */
  agentId: string;
}

/**
 * 智能体详情弹框组件 - 移动版
 */
const Mobile: React.FC<MobileAgentDetailModalProps> = ({ opened, onClose, agentId }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 获取智能体详情
  const { data: agentDetailResponse, isLoading } = useQuery({
    queryKey: ['agentDetail', agentId],
    queryFn: () => getDetail(agentId),
    enabled: opened && !!agentId,
    refetchOnWindowFocus: false,
  });

  const agentDetail = agentDetailResponse?.data as AgentDetail;

  // 使用支付Hook
  const {
    paymentMethod,
    handleAgentPurchase,
    isPaymentLoading,
    selectedPlan,
    paymentPlans,
    selectedPlanId,
    handleSelectPlan,
    isPaymentPlansLoading,
  } = useAgentPayment(agentId);

  return (
    <Modal.Root opened={opened} onClose={onClose} fullScreen>
      <Modal.Overlay />
      <Modal.Content>
        <Modal.Header>
          <Modal.Title>智能体详情</Modal.Title>
          <Modal.CloseButton />
        </Modal.Header>
        <div className="flex flex-col h-full overflow-auto">
          <div className="flex-1 overflow-auto p-4">
            {/* 智能体头像和基本信息 */}
            <div className="flex flex-col items-center mb-6">
              <div className="w-24 h-24 rounded-full overflow-hidden mb-4">
                <Image
                  src={agentDetail?.icon || '/default-agent-icon.png'}
                  alt={agentDetail?.name || '智能体'}
                  width={96}
                  height={96}
                  fit="cover"
                />
              </div>

              {/* 智能体名称和创建者 */}
              <div className="flex flex-col items-center gap-2">
                <div className="text-center text-xl font-semibold">{isLoading ? '加载中...' : agentDetail?.name}</div>
                <div className="flex items-center gap-1 text-gray-500">
                  <div className="flex flex-row items-center space-x-1">
                    <div className="text-sm">创建者：{agentDetail?.created_by || 'AI助手'}</div>
                  </div>
                </div>
              </div>

              {/* 智能体描述 */}
              <div className="max-w-md text-center text-sm font-normal mt-2">
                {agentDetail?.description || '这是一个智能AI助手，可以帮助您完成各种任务。'}
              </div>
            </div>

            {/* 统计信息 */}
            {/*<AgentStats agentDetail={agentDetail} isDark={isDark} isLoading={isLoading} />*/}

            {/* 对话开场白 */}
            <AgentConversationStarters agentDetail={agentDetail} isDark={isDark} isLoading={isLoading} />

            {/* 功能列表/付费计划列表 */}
            {agentDetail?.has_purchased ? (
              // 如果智能体已购买，显示功能列表
              <AgentFeatures agentDetail={agentDetail} isDark={isDark} isLoading={isLoading} />
            ) : (
              // 否则显示付费计划列表
              <PaymentPlanList
                plans={paymentPlans}
                isLoading={isLoading || isPaymentPlansLoading}
                selectedPlanId={selectedPlanId}
                onSelectPlan={handleSelectPlan}
              />
            )}

            {/* 评级 */}
            <AgentRatings agentDetail={agentDetail} isDark={isDark} isLoading={isLoading} />

            {/* 由同一创建者创建的更多智能体 */}
            <RelatedAgents agentDetail={agentDetail} isDark={isDark} agentId={agentId} onClose={onClose} isLoading={isLoading} />
          </div>

          {/* 底部聊天按钮 - 固定在底部 */}
          <div
            className="sticky bottom-0 left-0 right-0 z-20 p-4 mt-auto"
            style={{
              backgroundColor: isDark ? '#1a202c' : 'white',
            }}
          >
            {agentDetail?.has_purchased ? (
              // 已购买，显示开始聊天按钮
              <Link
                className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                  isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                } text-white`}
                to={`/chat?agent_id=${agentId}`}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M12 4C7.58172 4 4 7.58172 4 12C4 14.1941 4.88193 16.1802 6.31295 17.6265C6.6343 17.9513 6.69466 18.4526 6.45959 18.8443L5.76619 20H12C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22H4C3.63973 22 3.30731 21.8062 3.1298 21.4927C2.95229 21.1792 2.95715 20.7944 3.14251 20.4855L4.36137 18.4541C2.88894 16.7129 2 14.4595 2 12Z"
                    fill="currentColor"
                  />
                </svg>
                开始聊天
              </Link>
            ) : (
              // 未购买，显示支付按钮
              <div className="flex flex-col gap-3">
                <button
                  className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                    isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                  } text-white`}
                  onClick={() => {
                    if (agentDetail) {
                      handleAgentPurchase(agentDetail, 'alipay');
                    }
                  }}
                  disabled={paymentMethod === 'alipay' && isPaymentLoading}
                >
                  {paymentMethod === 'alipay' && isPaymentLoading ? (
                    <span>处理中...</span>
                  ) : (
                    <>
                      <FaAlipay size={20} color="#1677FF" />
                      支付宝 ¥{selectedPlan?.price || 0}
                    </>
                  )}
                </button>
                <button
                  className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                    isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                  } text-white`}
                  onClick={() => {
                    if (agentDetail) {
                      handleAgentPurchase(agentDetail, 'wechatpay');
                    }
                  }}
                  disabled={paymentMethod === 'wechatpay' && isPaymentLoading}
                >
                  {paymentMethod === 'wechatpay' && isPaymentLoading ? (
                    <span>处理中...</span>
                  ) : (
                    <>
                      <FaWeixin size={20} color="#09BB07" />
                      微信 ¥{selectedPlan?.price || 0}
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </Modal.Content>
    </Modal.Root>
  );
};

export default Mobile;
