import {Cha<PERSON>, CompletionChat, CreateAgentOrUpdate, Marketplace, Workflow} from "@/agents/pages";
import {GlobalLayout} from "@/common/layouts";
import {CourseDetail, CourseHome} from '@/course/pages';
import {
    CartPage,
    ForgetPassword,
    Login,
    OrderStatusPage,
    PrivacyPolicy,
    Register,
    TermsOfService,
    UserAssets
} from "@/user/pages";
import {useEffect} from "react";
import {HashRouter, Route, Routes} from 'react-router-dom';
import {useSiteSettingsStore} from "./common/stores";

function App() {
    const { loadSettings } = useSiteSettingsStore();
    useEffect(() => {
        loadSettings();
    }, []);
    return (
        <HashRouter>
            <Routes>
                {/* 登录 */}
                <Route path="/login" element={<Login />} />
                {/* 注册 */}
                <Route path="/register" element={<Register />} />
                {/* 忘记密码 */}
                <Route path="/forget-password" element={<ForgetPassword />} />


                <Route path="/" element={<GlobalLayout />}>
                    <Route index element={<Marketplace />} />
                    {/* 应用市场 */}
                    <Route path="marketplace/:mode?" element={<Marketplace />} />
                    <Route path="marketplace/create" element={<CreateAgentOrUpdate />} />
                    <Route path="marketplace/edit" element={<CreateAgentOrUpdate />} />
                    {/* 购物车 */}
                    <Route path="cart" element={<CartPage />} />
                    {/* 课程 */}
                    <Route path="course"  >
                        <Route index element={<CourseHome />} />
                        <Route path=":id" element={<CourseDetail />} />
                    </Route>
                    {/* 聊天 */}
                    <Route path="chat" element={<Chat />} />
                    {/* 工作流 */}
                    <Route path="workflow" element={<Workflow />} />
                    {/* 完成聊天 */}
                    <Route path="completion-chat" element={<CompletionChat />} />
                    {/* 用户资产 */}
                    <Route path="assets" element={<UserAssets />} />
                </Route>
                {/* 订单状态 */}
                <Route path="orderStatus" element={<OrderStatusPage />} />
                {/* 隐私政策 */}
                <Route path="privacy-policy" element={<PrivacyPolicy />} />
                {/* 用户协议 */}
                <Route path="terms-of-service" element={<TermsOfService />} />
            </Routes>
        </HashRouter>
    );
}

export default App;
