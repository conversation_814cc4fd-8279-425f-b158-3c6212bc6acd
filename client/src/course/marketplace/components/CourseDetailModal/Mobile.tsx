import React from 'react';
import { Modal, Image, Group, Text, Badge, List } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { useTheme } from '~/core/features/mantine';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getCourseDetail } from '../../api';
import { CourseDetail } from '../../schemas';
import { CourseStats, CourseRatings, RelatedCourses, CourseSectionList } from '../../components';
import { useCoursePayment } from '../../hooks';
import { PaymentPlanList } from '~/paymentPlan/components';
import { FaAlipay, FaWeixin } from 'react-icons/fa';
import './styles.css';

interface MobileCourseDetailModalProps {
  /**
   * 是否打开弹框
   */
  opened: boolean;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
  /**
   * 课程ID
   */
  courseId: string;
}

/**
 * 课程详情弹框组件 - 移动版
 */
const Mobile: React.FC<MobileCourseDetailModalProps> = ({ opened, onClose, courseId }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 获取课程详情
  const { data: courseDetailResponse, isLoading } = useQuery({
    queryKey: ['courseDetail', courseId],
    queryFn: () => getCourseDetail(courseId),
    enabled: opened && !!courseId,
    refetchOnWindowFocus: false,
  });

  const courseDetail = courseDetailResponse?.data as CourseDetail;

  // 使用支付Hook
  const {
    handleCoursePurchase,
    isPaymentLoading,
    selectedPlan,
    paymentPlans,
    selectedPlanId,
    handleSelectPlan,
    isPaymentPlansLoading,
  } = useCoursePayment(courseId);

  return (
    <Modal.Root opened={opened} onClose={onClose} fullScreen>
      <Modal.Overlay />
      <Modal.Content>
        <Modal.Header>
          <Modal.Title>课程详情</Modal.Title>
          <Modal.CloseButton />
        </Modal.Header>
        <div className="flex flex-col h-full overflow-auto">
          <div className="flex-1 overflow-auto p-4">
            {/* 课程头像和基本信息 */}
            <div className="flex flex-col items-center mb-6">
              <div className="w-24 h-24 rounded-full overflow-hidden mb-4">
                <Image
                  src={
                    courseDetail?.icon ||
                    courseDetail?.cover_image ||
                    'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743067298-ai.jpg'
                  }
                  alt={courseDetail?.name || '课程'}
                  width={96}
                  height={96}
                  fit="cover"
                />
              </div>

              {/* 课程名称和创建者 */}
              <div className="flex flex-col items-center gap-2">
                <div className="text-center text-xl font-semibold">{isLoading ? '加载中...' : courseDetail?.name}</div>
                <div className="flex items-center gap-1 text-gray-500">
                  <div className="flex flex-row items-center space-x-1">
                    <div className="text-sm">讲师：{courseDetail?.instructor || '未知'}</div>
                  </div>
                </div>
              </div>

              {/* 课程描述 */}
              <div className="max-w-md text-center text-sm font-normal mt-2">
                {courseDetail?.description || '这是一个精彩的课程，可以帮助您学习新知识。'}
              </div>
            </div>

            {/* 统计信息 */}
            {/*<CourseStats courseDetail={courseDetail} isDark={isDark} isLoading={isLoading} />*/}

            {/* 课程标签 */}
            {!isLoading && courseDetail?.tags && courseDetail.tags.length > 0 && (
              <div className="mt-4">
                <Text className="mb-2 font-medium">课程标签</Text>
                <Group gap="xs">
                  {courseDetail.tags.map((tag) => (
                    <Badge key={tag} color="blue" variant="light">
                      {tag}
                    </Badge>
                  ))}
                </Group>
              </div>
            )}

            {/* 课程章节 */}
            <CourseSectionList
              sections={courseDetail?.sections || []}
              isLoading={isLoading}
              hasPurchased={courseDetail?.has_purchased}
              maxVisibleSections={3} // 移动版显示更少的章节
              onSectionClick={(section) => {
                // 如果已购买或章节免费，可以跳转到章节详情页
                if (courseDetail?.has_purchased || section.is_free) {
                  // 这里可以添加跳转逻辑，例如：
                  // navigate(`/course/${courseId}/section/${section.id}`);
                  console.log('点击章节:', section);
                } else {
                  // 未购买且不是免费章节，提示用户购买
                  notifications.show({
                    title: '需要购买',
                    message: '请先购买课程以访问此章节',
                    color: 'yellow',
                  });
                }
              }}
            />

            {/* 功能列表/付费计划列表 */}
            {courseDetail?.has_purchased ? (
              // 如果课程已购买，显示功能列表
              <div className="mt-4">
                <Text className="mb-2 font-medium">课程功能</Text>
                <List spacing="xs" size="sm" center>
                  {courseDetail?.features?.map((feature, index) => (
                    <List.Item key={index}>
                      <Text size="sm">{feature}</Text>
                    </List.Item>
                  ))}
                </List>
              </div>
            ) : (
              // 否则显示付费计划列表
              <PaymentPlanList
                plans={paymentPlans}
                isLoading={isLoading || isPaymentPlansLoading}
                selectedPlanId={selectedPlanId}
                onSelectPlan={handleSelectPlan}
              />
            )}

            {/* 评级 */}
            <CourseRatings courseDetail={courseDetail} isDark={isDark} isLoading={isLoading} />

            {/* 由同一讲师创建的更多课程 */}
            <RelatedCourses
              courseDetail={courseDetail}
              isDark={isDark}
              courseId={courseId}
              onClose={onClose}
              isLoading={isLoading}
            />
          </div>

          {/* 底部按钮 - 固定在底部 */}
          <div
            className="sticky bottom-0 left-0 right-0 z-20 p-4 mt-auto"
            style={{
              backgroundColor: isDark ? '#1a202c' : 'white',
            }}
          >
            {courseDetail?.has_purchased ? (
              // 已购买，显示进入学习按钮
              <Link
                className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                  isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                } text-white`}
                to={`/course/${courseId}`}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5L16 12L10 7.5V16.5Z"
                    fill="currentColor"
                  />
                </svg>
                进入学习
              </Link>
            ) : (
              // 未购买，显示支付按钮
              <div className="flex flex-col gap-2">
                <button
                  className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                    isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                  } text-white`}
                  onClick={() => {
                    if (courseDetail) {
                      handleCoursePurchase(courseDetail, 'alipay');
                    }
                  }}
                  disabled={isPaymentLoading || !selectedPlanId}
                >
                  <FaAlipay size={20} color="#1677FF" />
                  支付宝 ¥{selectedPlan?.price || courseDetail?.price || 0}
                </button>
                <button
                  className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                    isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                  } text-white`}
                  onClick={() => {
                    if (courseDetail) {
                      handleCoursePurchase(courseDetail, 'wechatpay');
                    }
                  }}
                  disabled={isPaymentLoading || !selectedPlanId}
                >
                  <FaWeixin size={20} color="#09BB07" />
                  微信 ¥{selectedPlan?.price || courseDetail?.price || 0}
                </button>
              </div>
            )}
          </div>
        </div>
      </Modal.Content>
    </Modal.Root>
  );
};

export default Mobile;
