import { useState, useEffect, useCallback } from 'react';
import { CallBackProps, STATUS } from 'react-joyride';
import { useNavigate, useLocation } from 'react-router-dom';

/**
 * 本地存储键名
 */
const ONBOARDING_STORAGE_KEY = 'aizy-onboarding-completed';
const ONBOARDING_SKIPPED_KEY = 'aizy-onboarding-skipped';
const ONBOARDING_CURRENT_STEP_KEY = 'aizy-onboarding-current-step';
const ONBOARDING_SHOULD_START_KEY = 'aizy-onboarding-should-start';

/**
 * 新手引导状态管理 Hook
 */
export const useOnboarding = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [run, setRun] = useState(false);
  const [stepIndex, setStepIndex] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isSkipped, setIsSkipped] = useState(false);
  const [currentTourType, setCurrentTourType] = useState<'main' | 'chat' | 'agent'>('main');
  const [shouldStartFlag, setShouldStartFlag] = useState(false);

  /**
   * 检查用户是否已完成或跳过引导
   */
  useEffect(() => {
    const completed = localStorage.getItem(ONBOARDING_STORAGE_KEY) === 'true';
    const skipped = localStorage.getItem(ONBOARDING_SKIPPED_KEY) === 'true';

    setIsCompleted(completed);
    setIsSkipped(skipped);
  }, []);

  /**
   * 检查是否应该开始引导
   */
  useEffect(() => {
    // 只有在标记为应该开始引导时才自动开始
    if (shouldStartFlag && !isCompleted && !isSkipped && !run) {
      if (process.env.NODE_ENV === 'development') {
        console.log('开始新手引导，当前状态:', { shouldStartFlag, isCompleted, isSkipped, run });
      }

      // 清除标记
      localStorage.removeItem(ONBOARDING_SHOULD_START_KEY);
      setShouldStartFlag(false);

      // 延迟一秒开始，确保页面完全加载
      setTimeout(() => {
        if (process.env.NODE_ENV === 'development') {
          console.log('启动新手引导');
        }
        setCurrentTourType('main');
        setStepIndex(0);
        setRun(true);
      }, 1000);
    }
  }, [shouldStartFlag, isCompleted, isSkipped, run]);

  /**
   * 定期检查localStorage中的标记
   */
  useEffect(() => {
    const checkShouldStart = () => {
      const shouldStart = localStorage.getItem(ONBOARDING_SHOULD_START_KEY) === 'true';
      if (shouldStart !== shouldStartFlag) {
        setShouldStartFlag(shouldStart);
      }
    };

    // 每500ms检查一次
    const interval = setInterval(checkShouldStart, 500);

    return () => clearInterval(interval);
  }, [shouldStartFlag]);

  /**
   * 处理引导回调
   */
  const handleJoyrideCallback = useCallback((data: CallBackProps) => {
    const { status, type, index, action } = data;

    // 处理步骤变化
    if (type === 'step:after') {
      // 直接使用当前索引，不需要额外计算
      setStepIndex(index);
      localStorage.setItem(ONBOARDING_CURRENT_STEP_KEY, index.toString());
    }

    if (status === STATUS.FINISHED) {
      // 主引导完成，检查是否需要进入子页面引导
      if (currentTourType === 'main') {
        // 跳转到聊天页面开始子页面引导
        navigate('/chat');
        setTimeout(() => {
          setCurrentTourType('chat');
          setStepIndex(0);
          setRun(true);
        }, 500);
      } else if (currentTourType === 'chat') {
        // 聊天引导完成，跳转到智能体市场
        navigate('/agent/marketplace');
        setTimeout(() => {
          setCurrentTourType('agent');
          setStepIndex(0);
          setRun(true);
        }, 500);
      } else {
        // 所有引导完成
        setRun(false);
        setIsCompleted(true);
        localStorage.setItem(ONBOARDING_STORAGE_KEY, 'true');
        localStorage.removeItem(ONBOARDING_SKIPPED_KEY);
        localStorage.removeItem(ONBOARDING_CURRENT_STEP_KEY);
        localStorage.removeItem(ONBOARDING_SHOULD_START_KEY);
      }
    } else if (status === STATUS.SKIPPED) {
      // 用户跳过引导
      setRun(false);
      setIsSkipped(true);
      localStorage.setItem(ONBOARDING_SKIPPED_KEY, 'true');
      localStorage.removeItem(ONBOARDING_CURRENT_STEP_KEY);
      localStorage.removeItem(ONBOARDING_SHOULD_START_KEY);
    } else if (status === STATUS.ERROR) {
      // 引导出错，停止运行
      setRun(false);
      console.error('Onboarding tour error:', data);
    }
  }, [currentTourType, navigate]);

  /**
   * 开始引导
   */
  const startTour = useCallback(() => {
    setStepIndex(0);
    setRun(true);
    setIsCompleted(false);
    setIsSkipped(false);
  }, []);

  /**
   * 停止引导
   */
  const stopTour = useCallback(() => {
    setRun(false);
  }, []);

  /**
   * 重置引导状态
   */
  const resetTour = useCallback(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('重置引导状态');
    }
    localStorage.removeItem(ONBOARDING_STORAGE_KEY);
    localStorage.removeItem(ONBOARDING_SKIPPED_KEY);
    localStorage.removeItem(ONBOARDING_CURRENT_STEP_KEY);

    setIsCompleted(false);
    setIsSkipped(false);
    setStepIndex(0);
    setCurrentTourType('main');

    // 跳转到聊天首页
    navigate('/chat');

    // 延迟设置标记，确保页面跳转完成
    setTimeout(() => {
      if (process.env.NODE_ENV === 'development') {
        console.log('设置重新引导标记');
      }
      localStorage.setItem(ONBOARDING_SHOULD_START_KEY, 'true');
      setShouldStartFlag(true);
    }, 500);
  }, [navigate]);

  /**
   * 跳过引导
   */
  const skipTour = useCallback(() => {
    setRun(false);
    setIsSkipped(true);
    localStorage.setItem(ONBOARDING_SKIPPED_KEY, 'true');
  }, []);

  /**
   * 检查是否应该显示引导
   */
  const shouldShowTour = !isCompleted && !isSkipped;

  /**
   * 开始特定类型的引导
   */
  const startSpecificTour = useCallback((tourType: 'main' | 'chat' | 'agent') => {
    setCurrentTourType(tourType);
    setStepIndex(0);
    setRun(true);
    setIsCompleted(false);
    setIsSkipped(false);
  }, []);

  /**
   * 标记用户刚完成登录，应该开始引导
   */
  const markShouldStartOnboarding = useCallback(() => {
    const completed = localStorage.getItem(ONBOARDING_STORAGE_KEY) === 'true';
    const skipped = localStorage.getItem(ONBOARDING_SKIPPED_KEY) === 'true';

    if (process.env.NODE_ENV === 'development') {
      console.log('检查是否应该开始引导:', { completed, skipped });
    }

    // 只有在用户既没有完成也没有跳过引导时才标记
    if (!completed && !skipped) {
      if (process.env.NODE_ENV === 'development') {
        console.log('设置引导标记');
      }
      localStorage.setItem(ONBOARDING_SHOULD_START_KEY, 'true');
      setShouldStartFlag(true);
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log('用户已完成或跳过引导，不启动');
      }
    }
  }, []);

  return {
    // 状态
    run,
    stepIndex,
    isCompleted,
    isSkipped,
    shouldShowTour,
    currentTourType,
    shouldStartFlag,

    // 方法
    handleJoyrideCallback,
    startTour,
    stopTour,
    resetTour,
    skipTour,
    startSpecificTour,
    markShouldStartOnboarding,
  };
};

export default useOnboarding;
