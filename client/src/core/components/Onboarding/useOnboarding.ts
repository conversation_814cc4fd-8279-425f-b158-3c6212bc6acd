import { useState, useEffect, useCallback } from 'react';
import { CallBackProps, STATUS } from 'react-joyride';
import { useNavigate, useLocation } from 'react-router-dom';

/**
 * 本地存储键名
 */
const ONBOARDING_STORAGE_KEY = 'aizy-onboarding-completed';
const ONBOARDING_SKIPPED_KEY = 'aizy-onboarding-skipped';
const ONBOARDING_CURRENT_STEP_KEY = 'aizy-onboarding-current-step';

/**
 * 新手引导状态管理 Hook
 */
export const useOnboarding = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [run, setRun] = useState(false);
  const [stepIndex, setStepIndex] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isSkipped, setIsSkipped] = useState(false);
  const [currentTourType, setCurrentTourType] = useState<'main' | 'chat' | 'agent'>('main');

  /**
   * 检查用户是否已完成或跳过引导
   */
  useEffect(() => {
    const completed = localStorage.getItem(ONBOARDING_STORAGE_KEY) === 'true';
    const skipped = localStorage.getItem(ONBOARDING_SKIPPED_KEY) === 'true';
    
    setIsCompleted(completed);
    setIsSkipped(skipped);
    
    // 如果用户既没有完成也没有跳过引导，则自动开始
    if (!completed && !skipped) {
      // 延迟一秒开始，确保页面完全加载
      setTimeout(() => {
        setRun(true);
      }, 1000);
    }
  }, []);

  /**
   * 处理引导回调
   */
  const handleJoyrideCallback = useCallback((data: CallBackProps) => {
    const { status, type, index, action } = data;

    if (type === 'step:after') {
      setStepIndex(index + 1);
      // 保存当前步骤
      localStorage.setItem(ONBOARDING_CURRENT_STEP_KEY, (index + 1).toString());
    }

    if (status === STATUS.FINISHED) {
      // 主引导完成，检查是否需要进入子页面引导
      if (currentTourType === 'main') {
        // 跳转到聊天页面开始子页面引导
        navigate('/chat');
        setTimeout(() => {
          setCurrentTourType('chat');
          setStepIndex(0);
          setRun(true);
        }, 500);
      } else {
        // 所有引导完成
        setRun(false);
        setIsCompleted(true);
        localStorage.setItem(ONBOARDING_STORAGE_KEY, 'true');
        localStorage.removeItem(ONBOARDING_SKIPPED_KEY);
        localStorage.removeItem(ONBOARDING_CURRENT_STEP_KEY);
      }
    } else if (status === STATUS.SKIPPED) {
      // 用户跳过引导
      setRun(false);
      setIsSkipped(true);
      localStorage.setItem(ONBOARDING_SKIPPED_KEY, 'true');
      localStorage.removeItem(ONBOARDING_CURRENT_STEP_KEY);
    } else if (status === STATUS.ERROR) {
      // 引导出错，停止运行
      setRun(false);
      console.error('Onboarding tour error:', data);
    }
  }, [currentTourType, navigate]);

  /**
   * 开始引导
   */
  const startTour = useCallback(() => {
    setStepIndex(0);
    setRun(true);
    setIsCompleted(false);
    setIsSkipped(false);
  }, []);

  /**
   * 停止引导
   */
  const stopTour = useCallback(() => {
    setRun(false);
  }, []);

  /**
   * 重置引导状态
   */
  const resetTour = useCallback(() => {
    localStorage.removeItem(ONBOARDING_STORAGE_KEY);
    localStorage.removeItem(ONBOARDING_SKIPPED_KEY);
    localStorage.removeItem(ONBOARDING_CURRENT_STEP_KEY);
    setIsCompleted(false);
    setIsSkipped(false);
    setStepIndex(0);
    setCurrentTourType('main');

    // 跳转到聊天首页并开始引导
    navigate('/chat');
    setTimeout(() => {
      setRun(true);
    }, 500);
  }, [navigate]);

  /**
   * 跳过引导
   */
  const skipTour = useCallback(() => {
    setRun(false);
    setIsSkipped(true);
    localStorage.setItem(ONBOARDING_SKIPPED_KEY, 'true');
  }, []);

  /**
   * 检查是否应该显示引导
   */
  const shouldShowTour = !isCompleted && !isSkipped;

  /**
   * 开始特定类型的引导
   */
  const startSpecificTour = useCallback((tourType: 'main' | 'chat' | 'agent') => {
    setCurrentTourType(tourType);
    setStepIndex(0);
    setRun(true);
    setIsCompleted(false);
    setIsSkipped(false);
  }, []);

  return {
    // 状态
    run,
    stepIndex,
    isCompleted,
    isSkipped,
    shouldShowTour,
    currentTourType,

    // 方法
    handleJoyrideCallback,
    startTour,
    stopTour,
    resetTour,
    skipTour,
    startSpecificTour,
  };
};

export default useOnboarding;
