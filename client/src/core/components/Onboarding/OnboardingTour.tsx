import React, { useMemo } from 'react';
import Joyride, { ACTIONS, EVENTS, STATUS } from 'react-joyride';
import { useOnboarding } from './useOnboarding';
import { tourSteps, chatPageSteps, agentMarketSteps, completionStep, tourStyles } from './tourSteps';
import { useUserStore } from '~/user/core/store';
import { useLocation } from 'react-router-dom';

/**
 * 新手引导组件
 */
const OnboardingTour: React.FC = () => {
  const { isAuthenticated } = useUserStore();
  const location = useLocation();
  const {
    run,
    stepIndex,
    currentTourType,
    handleJoyrideCallback,
  } = useOnboarding();

  // 根据当前引导类型和页面选择对应的步骤
  const currentSteps = useMemo(() => {
    switch (currentTourType) {
      case 'main':
        return tourSteps;
      case 'chat':
        return [...chatPageSteps, completionStep];
      case 'agent':
        return [...agentMarketSteps, completionStep];
      default:
        return tourSteps;
    }
  }, [currentTourType]);

  // 只有在用户已登录时才显示引导
  if (!isAuthenticated) {
    return null;
  }

  return (
    <Joyride
      steps={currentSteps}
      run={run}
      stepIndex={stepIndex}
      callback={handleJoyrideCallback}
      continuous
      showProgress
      showSkipButton
      scrollToFirstStep
      disableOverlayClose
      disableCloseOnEsc={false}
      hideCloseButton={false}
      spotlightClicks={false}
      spotlightPadding={4}
      styles={{
        options: tourStyles.options,
        tooltip: tourStyles.tooltip,
        tooltipContainer: tourStyles.tooltipContainer,
        tooltipTitle: tourStyles.tooltipTitle,
        tooltipContent: tourStyles.tooltipContent,
        buttonNext: tourStyles.buttonNext,
        buttonBack: tourStyles.buttonBack,
        buttonSkip: tourStyles.buttonSkip,
        buttonClose: tourStyles.buttonClose,
      }}
      locale={{
        back: '上一步',
        close: '关闭',
        last: currentTourType === 'main' ? '继续' : '完成',
        next: '下一步',
        open: '打开对话框',
        skip: '跳过引导',
      }}
      disableScrolling={false}
      disableScrollParentFix={true}
      floaterProps={{
        disableAnimation: false,
        styles: {
          floater: {
            filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))',
          },
        },
      }}
    />
  );
};

export default OnboardingTour;
