import { API_BASE_URL, Pagination, ResponsePayloads } from '@/common';
import { useUserStore } from './stores';
import {
  AssetType,
  LoginRequest,
  LoginResult,
  Order,
  OrderCreateRequest,
  OrderResult,
  RegisterRequest,
  ResetPasswordRequest,
  SmsLoginRequest,
  UserAccountInfo,
  UserAssetItem,
  UserInfo
} from './types';
import { AgentType } from '@/agents/types';
import { request } from '@/common/utils';

/**
 * 用户登录
 *
 * @param data 登录请求参数
 * @returns 登录结果
 */
export async function login(
  data: LoginRequest
): Promise<ResponsePayloads<LoginResult>> {
  const response = await fetch(`${API_BASE_URL}/users/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json'
    },
    body: JSON.stringify(data)
  });

  return await response.json();
}
export async function me(): Promise<ResponsePayloads<UserInfo>> {
  const token = useUserStore.getState().token;
  const response = await request(`${API_BASE_URL}/users/me`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`
    }
  });
  return response.json();
}
/**
 * 发送验证码
 *
 * @param phone 手机号码
 */
export async function sendVerificationCode(
  phone: string
): Promise<ResponsePayloads<string>> {
  const response = await fetch(
    `${API_BASE_URL}/users/send_verification_code?phone=${phone}`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json'
      }
    }
  );

  return await response.json();
}
/**
 * 短信登录
 *
 * @param data 短信登录请求参数
 * @returns 登录结果
 */
export async function smsLogin(
  data: SmsLoginRequest
): Promise<ResponsePayloads<LoginResult>> {
  const response = await fetch(`${API_BASE_URL}/users/login/sms`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json'
    },
    body: JSON.stringify(data)
  });

  return await response.json();
}
/**
 * 重置密码
 *
 * @param data 重置密码请求参数
 * @returns 重置结果
 */
export async function resetPassword(
  data: ResetPasswordRequest
): Promise<ResponsePayloads<string>> {
  const response = await fetch(`${API_BASE_URL}/users/reset-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json'
    },
    body: JSON.stringify(data)
  });

  return await response.json();
}

/**
 * 用户注册
 *
 * @param data 注册请求参数
 * @returns 注册结果
 */
export async function register(
  data: RegisterRequest
): Promise<ResponsePayloads<LoginResult>> {
  const response = await fetch(`${API_BASE_URL}/users/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json'
    },
    body: JSON.stringify(data)
  });

  return await response.json();
}

export async function createOrder(
  data: OrderCreateRequest
): Promise<ResponsePayloads<OrderResult>> {
  const token = useUserStore.getState().token;
  const response = await request(`${API_BASE_URL}/orders/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${token}`
    },
    body: JSON.stringify(data)
  });

  return await response.json();
}

/**
 * 获取用户资产
 *
 * @param assetType 资产类型
 * @param assetName 资产名称
 * @param page 页码
 * @param pageSize 每页数量
 */
export async function fetchUserAssets(
  assetType: AssetType,
  appMode?: AgentType,
  assetName: string = '',
  page = 1,
  pageSize = 100,
  includeDetails = false
): Promise<ResponsePayloads<Pagination<UserAssetItem>>> {
  const token = useUserStore.getState().token;
  const queryParams = new URLSearchParams();
  queryParams.append('asset_type', assetType);
  queryParams.append('asset_name', assetName);
  queryParams.append('page', page.toString());
  queryParams.append('page_size', pageSize.toString());
  if (appMode) {
    queryParams.append('app_mode', appMode);
  }
  if (includeDetails) {
    queryParams.append('include_details', 'true');
  }
  const response = await request(
    `${API_BASE_URL}/users/assets?${queryParams.toString()}`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token}`
      }
    }
  );
  return response.json();
}

export async function fetchOrderDetail(
  orderId: string
): Promise<ResponsePayloads<Order>> {
  const token = useUserStore.getState().token;
  const response = await request(
    `${API_BASE_URL}/orders/detail?order_id=${orderId}`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token}`
      }
    }
  );
  return response.json();
}

/**
 * 获取用户账户信息
 *
 * @returns 用户账户信息，包括余额、剩余体验次数和剩余token数量
 */
export async function fetchUserAccount(): Promise<
  ResponsePayloads<UserAccountInfo>
> {
  const token = useUserStore.getState().token;
  const response = await request(`${API_BASE_URL}/users/account`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`
    }
  });
  return response.json();
}
