import React, { useState } from 'react';
import {
  Container,
  Box,
  Text,
  Stack,
  Group,
  useMantineTheme,
  Table,
  Badge,
  Progress,
  Card,
  Loader,
  Center,
  Tabs,
} from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiKeyLine, RiBarChartLine } from 'react-icons/ri';
import { useQuery } from '@tanstack/react-query';

import dayjs from 'dayjs';

import { ModuleTitle } from '../components';
import { getUserActivities, getUserKeys } from '../api/usage';

const Usage = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const [activeTab, setActiveTab] = useState<string>('keys');

  const bgColor = isDark ? theme.colors.dark[7] : 'white';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const tableBgColor = isDark ? theme.colors.dark[6] : 'white';

  // 获取用户活动列表
  const { data: activitiesResponse, isLoading: isActivitiesLoading } = useQuery({
    queryKey: ['user-activities'],
    queryFn: getUserActivities,
  });

  // 获取用户密钥列表
  const { data: keysResponse, isLoading: isKeysLoading } = useQuery({
    queryKey: ['user-keys'],
    queryFn: getUserKeys,
  });

  const activities = activitiesResponse?.data?.activities || [];
  const keys = keysResponse?.data?.keys || [];

  const getServiceName = (serviceCode: string) => {
    switch (serviceCode) {
      case 'AGC':
        return '智能体聊天';
      case 'SZR':
        return '数字人';
      case 'DGV':
        return '数字人';
      case 'COU':
        return '课程';
      default:
        return serviceCode;
    }
  };

  const formatDate = (dateString: string) => {
    return dayjs(dateString).format('YYYY-MM-DD HH:mm');
  };

  const getCurrencySymbol = (currencyType: string) => {
    switch (currencyType) {
      case 'CNY':
        return '¥';
      case 'USD':
        return '$';
      case 'LTC':
        return 'Token';
      case 'UTS':
        return '次';
      default:
        return '';
    }
  };

  const getCurrencyName = (currencyType: string) => {
    switch (currencyType) {
      case 'CNY':
        return '人民币';
      case 'USD':
        return '美元';
      case 'LTC':
        return 'token';
      case 'UTS':
        return '次';
      default:
        return currencyType;
    }
  };

  const formatNumber = (num: number, currencyType: string) => {
    // 处理浮点数精度问题
    const roundedNum = Math.round(num * 100) / 100;

    // 对于LTC类型的大数字，转换为万为单位
    if (currencyType === 'LTC' && roundedNum >= 10000) {
      return (roundedNum / 10000).toFixed(1) + '万';
    }

    // 对于货币类型，保留2位小数
    if (currencyType === 'CNY' || currencyType === 'USD') {
      return roundedNum.toFixed(2);
    }

    // 对于Token类型，如果是整数则不显示小数
    if (currencyType === 'LTC' || currencyType === 'UTS') {
      return roundedNum % 1 === 0 ? roundedNum.toString() : roundedNum.toFixed(2);
    }

    return roundedNum.toString();
  };

  const formatNumberWithUnit = (num: number, currencyType: string) => {
    const formattedNum = formatNumber(num, currencyType);
    const symbol = getCurrencySymbol(currencyType);
    return `${formattedNum} ${symbol}`;
  };

  // 计算统计数据
  const activeKeys = keys.length;

  // 计算当日活动次数
  const today = dayjs().format('YYYY-MM-DD');
  const todayActivities = activities.filter((activity) => dayjs(activity.created_at).format('YYYY-MM-DD') === today).length;

  // 计算当日使用量（从当日活动中统计）
  const todayUsageByType = activities
    .filter((activity) => dayjs(activity.created_at).format('YYYY-MM-DD') === today)
    .reduce(
      (acc, activity) => {
        if (activity.amount && activity.currency_type) {
          const currencyType = activity.currency_type;
          if (!acc[currencyType]) {
            acc[currencyType] = 0;
          }
          acc[currencyType] += activity.amount;
        }
        return acc;
      },
      {} as Record<string, number>,
    );

  // 按currency_type分组统计总使用量和剩余用量
  const currencyStats = keys.reduce(
    (acc, key) => {
      const currencyType = key.currency_type;
      if (!acc[currencyType]) {
        acc[currencyType] = {
          totalUsed: 0,
          totalLimit: 0,
          remaining: 0,
          todayUsed: todayUsageByType[currencyType] || 0,
          count: 0,
        };
      }
      acc[currencyType].totalUsed += key.credit_used;
      acc[currencyType].totalLimit += key.credit_limit;
      acc[currencyType].remaining += key.credit_limit - key.credit_used;
      acc[currencyType].count += 1;
      return acc;
    },
    {} as Record<string, { totalUsed: number; totalLimit: number; remaining: number; todayUsed: number; count: number }>,
  );

  // 渲染活动列表
  const renderActivities = () => {
    if (isActivitiesLoading) {
      return (
        <Center h={400}>
          <Loader size="lg" />
        </Center>
      );
    }

    if (activities.length === 0) {
      return (
        <Box className="rounded-[14px] flex items-center justify-center" h={400} bg={bgColor}>
          <Stack align="center" gap="md">
            <RiBarChartLine size={48} color={isDark ? theme.colors.gray[6] : theme.colors.gray[5]} />
            <Text fz={16} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
              暂无活动记录
            </Text>
          </Stack>
        </Box>
      );
    }

    const activityRows = activities.map((activity) => {
      return (
        <Table.Tr key={activity.id}>
          <Table.Td>
            <Text fz={14} fw={500} c={textColor}>
              {getServiceName(activity.service_code)}
            </Text>
          </Table.Td>
          <Table.Td>
            <Text fz={12} c={textColor}>
              {activity.type === 'consume' ? '消费' : activity.type === 'verify' ? '验证' : activity.type}
            </Text>
          </Table.Td>
          <Table.Td>
            {activity.scope_names && activity.scope_names.length > 0 ? (
              <Stack gap={1}>
                {activity.scope_names.map((scopeName, index) => (
                  <Text key={index} fz={12} c={textColor}>
                    {scopeName}
                  </Text>
                ))}
              </Stack>
            ) : (
              <Text fz={12} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
                -
              </Text>
            )}
          </Table.Td>
          <Table.Td>
            <Text fz={12} c={textColor}>
              {activity.amount ? formatNumberWithUnit(activity.amount, activity.currency_type || '') : '-'}
            </Text>
          </Table.Td>
          <Table.Td>
            <Text fz={12} c={textColor}>
              {formatDate(activity.created_at)}
            </Text>
          </Table.Td>
        </Table.Tr>
      );
    });

    return (
      <Box className="rounded-[12px] overflow-hidden" bg={tableBgColor}>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>服务信息</Table.Th>
              <Table.Th>活动类型</Table.Th>
              <Table.Th>服务名称</Table.Th>
              <Table.Th>消耗</Table.Th>
              <Table.Th>时间</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{activityRows}</Table.Tbody>
        </Table>
      </Box>
    );
  };

  // 渲染密钥列表
  const renderKeys = () => {
    if (isKeysLoading) {
      return (
        <Center h={400}>
          <Loader size="lg" />
        </Center>
      );
    }

    if (keys.length === 0) {
      return (
        <Box className="rounded-[14px] flex items-center justify-center" h={400} bg={bgColor}>
          <Stack align="center" gap="md">
            <RiKeyLine size={48} color={isDark ? theme.colors.gray[6] : theme.colors.gray[5]} />
            <Text fz={16} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
              暂无API密钥
            </Text>
          </Stack>
        </Box>
      );
    }

    const keyRows = keys.map((key) => (
      <Table.Tr key={key.id}>
        <Table.Td>
          <Text fz={14} fw={500} c={textColor}>
            {getServiceName(key.service_code)}
          </Text>
        </Table.Td>
        <Table.Td>
          <Badge size="sm" color="green" variant="light">
            可用
          </Badge>
        </Table.Td>
        <Table.Td>
          <Stack gap={4}>
            <Group justify="space-between">
              <Text fz={12} c={textColor}>
                {formatNumberWithUnit(key.credit_used, key.currency_type)}/
                {formatNumberWithUnit(key.credit_limit, key.currency_type)}
              </Text>
              <Text fz={12} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
                {Math.round((key.credit_used / key.credit_limit) * 100)}%
              </Text>
            </Group>
            <Progress
              value={(key.credit_used / key.credit_limit) * 100}
              size="sm"
              color={key.credit_used / key.credit_limit > 0.8 ? 'red' : 'blue'}
            />
            <Text fz={12} c="orange">
              剩余 {formatNumberWithUnit(key.credit_limit - key.credit_used, key.currency_type)}
            </Text>
          </Stack>
        </Table.Td>
        <Table.Td>
          {(key.scope_names && key.scope_names.length > 0) || (key.scope && key.scope.length > 0) ? (
            <Stack gap={4}>
              {key.scope_names && key.scope_names.length > 0 ?
                key.scope_names.map((name, index) => (
                  <Text key={`name-${index}`} fz={12} c={textColor}>
                    {name}
                  </Text>
                )) :
                key.scope && key.scope.map((agentId, index) => (
                  <Text key={agentId} fz={12} c={textColor}>
                    {agentId}
                  </Text>
                ))
              }
            </Stack>
          ) : (
            <Text fz={12} c={textColor}>
              ————
            </Text>
          )}
        </Table.Td>
        <Table.Td>
          <Stack gap={2}>
            <Text fz={12} c={textColor}>
              创建: {formatDate(key.created_at)}
            </Text>
            <Text fz={12} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
              {key.expires_at ? `过期: ${formatDate(key.expires_at)}` : '永不过期'}
            </Text>
          </Stack>
        </Table.Td>
      </Table.Tr>
    ));

    return (
      <Box className="rounded-[12px] overflow-hidden" bg={tableBgColor}>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>服务类型</Table.Th>
              <Table.Th>状态</Table.Th>
              <Table.Th>使用情况</Table.Th>
              <Table.Th>服务内容</Table.Th>
              <Table.Th>时间信息</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{keyRows}</Table.Tbody>
        </Table>
      </Box>
    );
  };

  return (
    <Container size="1176px" pt={72} pb={72}>
      <Group justify="space-between" align="center" mb={32}>
        <ModuleTitle title="用量管理" />
      </Group>

      {/* 使用统计卡片 */}
      <Group mb={32} gap="lg">
        <Card className="rounded-[12px]" bg={bgColor} p="lg" style={{ flex: 1 }}>
          <Stack gap="xs">
            <Text fz={14} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
              当日活动次数
            </Text>
            <Text fz={24} fw={700} c={textColor}>
              {todayActivities}
            </Text>
            <Text fz={12} c="green">
              次
            </Text>
          </Stack>
        </Card>

        <Card className="rounded-[12px]" bg={bgColor} p="lg" style={{ flex: 1 }}>
          <Stack gap="xs">
            <Text fz={14} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
              活跃密钥
            </Text>
            <Text fz={24} fw={700} c={textColor}>
              {activeKeys}
            </Text>
            <Text fz={12} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
              全部可用
            </Text>
          </Stack>
        </Card>

        {/* 按currency_type分类显示使用量统计 */}
        {Object.entries(currencyStats)
          .filter(([, stats]) => stats.todayUsed > 0)
          .map(([currencyType, stats]) => (
            <Card key={currencyType} className="rounded-[12px]" bg={bgColor} p="lg" style={{ flex: 1 }}>
              <Stack gap="xs">
                <Text fz={14} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
                  当日使用量
                </Text>
                <Text fz={24} fw={700} c={textColor}>
                  {formatNumber(stats.todayUsed, currencyType)}
                </Text>
                <Text fz={12} c="orange">
                  {getCurrencyName(currencyType)} ({getCurrencySymbol(currencyType)})
                </Text>
              </Stack>
            </Card>
          ))}
      </Group>

      {/* 标签页 */}
      <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'keys')} mb="lg">
        <Tabs.List>
          <Tabs.Tab value="keys" leftSection={<RiKeyLine size={16} />}>
            API密钥
          </Tabs.Tab>
          <Tabs.Tab value="activities" leftSection={<RiBarChartLine size={16} />}>
            活动记录
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="keys" pt="lg">
          {renderKeys()}
        </Tabs.Panel>

        <Tabs.Panel value="activities" pt="lg">
          {renderActivities()}
        </Tabs.Panel>
      </Tabs>
    </Container>
  );
};

export default Usage;
