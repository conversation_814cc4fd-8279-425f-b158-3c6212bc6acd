/*
 Navicat Premium Dump SQL

 Source Server         : localhost
 Source Server Type    : PostgreSQL
 Source Server Version : 170003 (170003)
 Source Host           : localhost:5432
 Source Catalog        : xjkj
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170003 (170003)
 File Encoding         : 65001

 Date: 23/05/2025 11:25:13
*/


-- ----------------------------
-- Type structure for account_owner_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."account_owner_type_enum";
CREATE TYPE "public"."account_owner_type_enum" AS ENUM (
  'USER',
  'PLATFORM'
);
ALTER TYPE "public"."account_owner_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for action_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."action_type_enum";
CREATE TYPE "public"."action_type_enum" AS ENUM (
  'VIEW',
  'USE',
  'DOWNLOAD',
  'SHARE',
  'COMPLETE',
  'CHAT'
);
ALTER TYPE "public"."action_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for activity_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."activity_status_enum";
CREATE TYPE "public"."activity_status_enum" AS ENUM (
  'DRAFT',
  'ACTIVE',
  'ENDED',
  'CANCELLED'
);
ALTER TYPE "public"."activity_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for agent_order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."agent_order_status_enum";
CREATE TYPE "public"."agent_order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."agent_order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for agent_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."agent_type_enum";
CREATE TYPE "public"."agent_type_enum" AS ENUM (
  'CHAT',
  'TEXT',
  'WORKFLOW'
);
ALTER TYPE "public"."agent_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for consult_order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."consult_order_status_enum";
CREATE TYPE "public"."consult_order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."consult_order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for coupon_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."coupon_status_enum";
CREATE TYPE "public"."coupon_status_enum" AS ENUM (
  'UNUSED',
  'USED',
  'EXPIRED'
);
ALTER TYPE "public"."coupon_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for coupon_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."coupon_type_enum";
CREATE TYPE "public"."coupon_type_enum" AS ENUM (
  'FIXED',
  'PERCENT'
);
ALTER TYPE "public"."coupon_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for course_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."course_type_enum";
CREATE TYPE "public"."course_type_enum" AS ENUM (
  'SY',
  'CH',
  'YX',
  'TRZ',
  'GL',
  'WXXL',
  'LQGX',
  'QLSD',
  'JWH',
  'LQKY',
  'LTJX',
  'LYWY',
  'QLYJ',
  'YY',
  'LBBXK',
  'GLZBXK',
  'ZCJSBD',
  'AIYXJT',
  'AIYFJT',
  'ZNTSZ'
);
ALTER TYPE "public"."course_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for gender_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."gender_enum";
CREATE TYPE "public"."gender_enum" AS ENUM (
  'MALE',
  'FEMALE',
  'UNKNOWN'
);
ALTER TYPE "public"."gender_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for member_order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."member_order_status_enum";
CREATE TYPE "public"."member_order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."member_order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for order_status_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."order_status_enum";
CREATE TYPE "public"."order_status_enum" AS ENUM (
  'PENDING',
  'PAID',
  'CANCELLED',
  'REFUNDED'
);
ALTER TYPE "public"."order_status_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for resource_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."resource_type_enum";
CREATE TYPE "public"."resource_type_enum" AS ENUM (
  'AGENT',
  'COURSE',
  'COURSE_SECTION'
);
ALTER TYPE "public"."resource_type_enum" OWNER TO "admin";

-- ----------------------------
-- Type structure for user_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."user_type_enum";
CREATE TYPE "public"."user_type_enum" AS ENUM (
  'WECHAT',
  'LOCAL'
);
ALTER TYPE "public"."user_type_enum" OWNER TO "admin";

-- ----------------------------
-- Sequence structure for asm_activities_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_activities_id_seq";
CREATE SEQUENCE "public"."asm_activities_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_activity_reward_records_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_activity_reward_records_id_seq";
CREATE SEQUENCE "public"."asm_activity_reward_records_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_agent_orders_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_agent_orders_id_seq";
CREATE SEQUENCE "public"."asm_agent_orders_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_agent_starters_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_agent_starters_id_seq";
CREATE SEQUENCE "public"."asm_agent_starters_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_agents_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_agents_id_seq";
CREATE SEQUENCE "public"."asm_agents_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_consult_orders_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_consult_orders_id_seq";
CREATE SEQUENCE "public"."asm_consult_orders_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_coupons_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_coupons_id_seq";
CREATE SEQUENCE "public"."asm_coupons_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_course_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_course_id_seq";
CREATE SEQUENCE "public"."asm_course_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_course_orders_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_course_orders_id_seq";
CREATE SEQUENCE "public"."asm_course_orders_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_course_section_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_course_section_id_seq";
CREATE SEQUENCE "public"."asm_course_section_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_investment_show_application_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_investment_show_application_id_seq";
CREATE SEQUENCE "public"."asm_investment_show_application_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_investment_show_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_investment_show_id_seq";
CREATE SEQUENCE "public"."asm_investment_show_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_member_orders_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_member_orders_id_seq";
CREATE SEQUENCE "public"."asm_member_orders_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_system_parameters_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_system_parameters_id_seq";
CREATE SEQUENCE "public"."asm_system_parameters_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_teacher_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_teacher_id_seq";
CREATE SEQUENCE "public"."asm_teacher_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_user_accounts_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_user_accounts_id_seq";
CREATE SEQUENCE "public"."asm_user_accounts_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_user_usages_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_user_usages_id_seq";
CREATE SEQUENCE "public"."asm_user_usages_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for asm_users_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."asm_users_id_seq";
CREATE SEQUENCE "public"."asm_users_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE **********
START 1
CACHE 1;

-- ----------------------------
-- Table structure for alembic_version
-- ----------------------------
DROP TABLE IF EXISTS "public"."alembic_version";
CREATE TABLE "public"."alembic_version" (
  "version_num" varchar(32) COLLATE "pg_catalog"."default" NOT NULL
)
;

-- ----------------------------
-- Records of alembic_version
-- ----------------------------
INSERT INTO "public"."alembic_version" VALUES ('b03635dcf4a0');

-- ----------------------------
-- Table structure for asm_activities
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_activities";
CREATE TABLE "public"."asm_activities" (
  "id" int4 NOT NULL DEFAULT nextval('asm_activities_id_seq'::regclass),
  "title" varchar COLLATE "pg_catalog"."default",
  "start_time" timestamp(6),
  "end_time" timestamp(6),
  "status" "public"."activity_status_enum",
  "conditions" varchar COLLATE "pg_catalog"."default",
  "rewards" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_activities"."id" IS '活动ID';
COMMENT ON COLUMN "public"."asm_activities"."title" IS '活动标题';
COMMENT ON COLUMN "public"."asm_activities"."start_time" IS '活动开始时间';
COMMENT ON COLUMN "public"."asm_activities"."end_time" IS '活动结束时间';
COMMENT ON COLUMN "public"."asm_activities"."status" IS '活动状态';
COMMENT ON COLUMN "public"."asm_activities"."conditions" IS '达成条件(JSON格式)';
COMMENT ON COLUMN "public"."asm_activities"."rewards" IS '奖励内容(JSON格式)';
COMMENT ON COLUMN "public"."asm_activities"."created_at" IS '创建时间';
COMMENT ON TABLE "public"."asm_activities" IS '活动表';

-- ----------------------------
-- Records of asm_activities
-- ----------------------------
INSERT INTO "public"."asm_activities" VALUES (2, '聊天送优惠券', '2025-01-18 16:00:00', '2044-01-06 16:00:00', 'DRAFT', '{"type":"CHAT_COUNT","params":{"required_count":3}}', '{"type":"coupon","coupons":[{"name":"限时特惠券","code":"SPECIAL_100","type":"FIXED","value":1,"minAmount":500,"status":"UNUSED"}]}', '2025-01-18 20:54:20.823');
INSERT INTO "public"."asm_activities" VALUES (3, '新人优惠券', '2025-01-20 16:00:00', '2029-12-22 16:00:00', 'ACTIVE', '{"type":"NEWUSER_COUPON"}', '{"type":"coupon","coupons":[{"name":"新人优惠券","code":"NEWUSER_100","type":"FIXED","value":100,"minAmount":0,"status":"UNUSED"}]}', '2025-01-20 23:33:59.248');

-- ----------------------------
-- Table structure for asm_activity_reward_records
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_activity_reward_records";
CREATE TABLE "public"."asm_activity_reward_records" (
  "id" int4 NOT NULL DEFAULT nextval('asm_activity_reward_records_id_seq'::regclass),
  "user_id" int4,
  "reward_type" varchar COLLATE "pg_catalog"."default",
  "reward_content" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_activity_reward_records"."id" IS '记录ID';
COMMENT ON COLUMN "public"."asm_activity_reward_records"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_activity_reward_records"."reward_type" IS '奖励类型';
COMMENT ON COLUMN "public"."asm_activity_reward_records"."reward_content" IS '领取的奖励内容(JSON格式)';
COMMENT ON COLUMN "public"."asm_activity_reward_records"."created_at" IS '领取时间';
COMMENT ON TABLE "public"."asm_activity_reward_records" IS '活动奖励领取记录表';

-- ----------------------------
-- Records of asm_activity_reward_records
-- ----------------------------
INSERT INTO "public"."asm_activity_reward_records" VALUES (2, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 10:18:43.942914');
INSERT INTO "public"."asm_activity_reward_records" VALUES (3, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 16:14:08.991505');
INSERT INTO "public"."asm_activity_reward_records" VALUES (4, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 19:01:35.45559');
INSERT INTO "public"."asm_activity_reward_records" VALUES (5, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 19:03:06.141307');
INSERT INTO "public"."asm_activity_reward_records" VALUES (6, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 19:12:33.553939');
INSERT INTO "public"."asm_activity_reward_records" VALUES (7, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:24:40.904141');
INSERT INTO "public"."asm_activity_reward_records" VALUES (8, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:28:03.975716');
INSERT INTO "public"."asm_activity_reward_records" VALUES (9, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:28:28.127166');
INSERT INTO "public"."asm_activity_reward_records" VALUES (10, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:28:56.699599');
INSERT INTO "public"."asm_activity_reward_records" VALUES (11, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:29:54.393432');
INSERT INTO "public"."asm_activity_reward_records" VALUES (12, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:31:28.08881');
INSERT INTO "public"."asm_activity_reward_records" VALUES (13, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:48:09.501789');
INSERT INTO "public"."asm_activity_reward_records" VALUES (14, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:51:04.10741');
INSERT INTO "public"."asm_activity_reward_records" VALUES (15, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 20:51:45.666019');
INSERT INTO "public"."asm_activity_reward_records" VALUES (16, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-21 21:01:36.824595');
INSERT INTO "public"."asm_activity_reward_records" VALUES (17, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 06:31:19.002754');
INSERT INTO "public"."asm_activity_reward_records" VALUES (18, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 07:16:15.773368');
INSERT INTO "public"."asm_activity_reward_records" VALUES (19, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 07:20:28.59565');
INSERT INTO "public"."asm_activity_reward_records" VALUES (20, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 19:47:30.449132');
INSERT INTO "public"."asm_activity_reward_records" VALUES (21, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-22 23:46:55.668831');
INSERT INTO "public"."asm_activity_reward_records" VALUES (22, 9, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 11:15:34.963148');
INSERT INTO "public"."asm_activity_reward_records" VALUES (23, 9, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 11:15:36.181891');
INSERT INTO "public"."asm_activity_reward_records" VALUES (24, 9, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 11:15:36.28202');
INSERT INTO "public"."asm_activity_reward_records" VALUES (25, 8, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 11:21:35.234343');
INSERT INTO "public"."asm_activity_reward_records" VALUES (26, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 15:13:24.300311');
INSERT INTO "public"."asm_activity_reward_records" VALUES (27, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 15:16:23.988667');
INSERT INTO "public"."asm_activity_reward_records" VALUES (28, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-23 15:16:53.687323');
INSERT INTO "public"."asm_activity_reward_records" VALUES (29, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 18:06:56.732037');
INSERT INTO "public"."asm_activity_reward_records" VALUES (30, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 18:12:26.640841');
INSERT INTO "public"."asm_activity_reward_records" VALUES (31, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 18:24:04.3662');
INSERT INTO "public"."asm_activity_reward_records" VALUES (32, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 18:45:58.712057');
INSERT INTO "public"."asm_activity_reward_records" VALUES (33, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 19:55:18.581655');
INSERT INTO "public"."asm_activity_reward_records" VALUES (34, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 20:01:51.282289');
INSERT INTO "public"."asm_activity_reward_records" VALUES (35, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 20:03:53.21793');
INSERT INTO "public"."asm_activity_reward_records" VALUES (36, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-24 20:19:44.646593');
INSERT INTO "public"."asm_activity_reward_records" VALUES (37, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-25 10:05:46.666452');
INSERT INTO "public"."asm_activity_reward_records" VALUES (38, 9, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-25 10:05:50.632969');
INSERT INTO "public"."asm_activity_reward_records" VALUES (39, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-25 15:11:55.078446');
INSERT INTO "public"."asm_activity_reward_records" VALUES (40, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-26 14:37:37.537703');
INSERT INTO "public"."asm_activity_reward_records" VALUES (41, 3, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-26 14:43:13.366428');
INSERT INTO "public"."asm_activity_reward_records" VALUES (42, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-01-26 14:48:41.651645');
INSERT INTO "public"."asm_activity_reward_records" VALUES (43, 18, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-13 21:28:33.454503');
INSERT INTO "public"."asm_activity_reward_records" VALUES (44, 8, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-15 12:25:37.508723');
INSERT INTO "public"."asm_activity_reward_records" VALUES (45, 19, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-17 10:09:10.150937');
INSERT INTO "public"."asm_activity_reward_records" VALUES (46, 25, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-18 10:15:19.857614');
INSERT INTO "public"."asm_activity_reward_records" VALUES (47, 25, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-18 10:24:01.049434');
INSERT INTO "public"."asm_activity_reward_records" VALUES (48, 22, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-18 10:25:55.446272');
INSERT INTO "public"."asm_activity_reward_records" VALUES (49, 28, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-20 14:52:23.11455');
INSERT INTO "public"."asm_activity_reward_records" VALUES (50, 28, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-20 15:06:00.894');
INSERT INTO "public"."asm_activity_reward_records" VALUES (51, 35, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-20 15:18:48.094575');
INSERT INTO "public"."asm_activity_reward_records" VALUES (52, 37, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-20 16:03:55.915814');
INSERT INTO "public"."asm_activity_reward_records" VALUES (53, 10, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-25 11:18:16.356133');
INSERT INTO "public"."asm_activity_reward_records" VALUES (54, 49, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-02-26 11:34:32.712419');
INSERT INTO "public"."asm_activity_reward_records" VALUES (55, 55, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-05 12:40:18.751008');
INSERT INTO "public"."asm_activity_reward_records" VALUES (56, 58, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-06 15:18:11.650924');
INSERT INTO "public"."asm_activity_reward_records" VALUES (57, 57, 'coupon', '[{"name": "\u65b0\u4eba\u4f18\u60e0\u5238", "code": "NEWUSER_100", "type": "FIXED", "value": 100, "minAmount": 0, "status": "UNUSED"}]', '2025-03-07 15:37:21.016388');

-- ----------------------------
-- Table structure for asm_admins
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_admins";
CREATE TABLE "public"."asm_admins" (
  "id" int4 NOT NULL DEFAULT nextval('asm_users_id_seq'::regclass),
  "username" varchar(50) COLLATE "pg_catalog"."default",
  "password" varchar(100) COLLATE "pg_catalog"."default",
  "avatar_url" text COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_admins"."id" IS '管理员ID';
COMMENT ON COLUMN "public"."asm_admins"."username" IS '用户名';
COMMENT ON COLUMN "public"."asm_admins"."password" IS '密码';
COMMENT ON COLUMN "public"."asm_admins"."avatar_url" IS '头像URL';
COMMENT ON COLUMN "public"."asm_admins"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_admins"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_admins" IS '管理员表';

-- ----------------------------
-- Records of asm_admins
-- ----------------------------
INSERT INTO "public"."asm_admins" VALUES (5, 'dongjak', '$2a$12$EjonnAhGY4WActrvn77r..AqBgouGYuPWfJDmplV7cvf8oPZ2gyDK', NULL, '2025-01-16 22:12:34', '2025-01-16 22:12:36');

-- ----------------------------
-- Table structure for asm_agent_orders
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_agent_orders";
CREATE TABLE "public"."asm_agent_orders" (
  "id" int4 NOT NULL DEFAULT nextval('asm_agent_orders_id_seq'::regclass),
  "order_no" varchar COLLATE "pg_catalog"."default",
  "agent_id" int4,
  "user_id" int4,
  "amount" float8,
  "status" "public"."agent_order_status_enum",
  "pay_time" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_agent_orders"."id" IS '订单ID';
COMMENT ON COLUMN "public"."asm_agent_orders"."order_no" IS '订单编号';
COMMENT ON COLUMN "public"."asm_agent_orders"."agent_id" IS '智能体ID';
COMMENT ON COLUMN "public"."asm_agent_orders"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_agent_orders"."amount" IS '订单金额';
COMMENT ON COLUMN "public"."asm_agent_orders"."status" IS '订单状态';
COMMENT ON COLUMN "public"."asm_agent_orders"."pay_time" IS '支付时间';
COMMENT ON COLUMN "public"."asm_agent_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_agent_orders"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_agent_orders" IS '智能体订单表';

-- ----------------------------
-- Records of asm_agent_orders
-- ----------------------------

-- ----------------------------
-- Table structure for asm_agent_starters
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_agent_starters";
CREATE TABLE "public"."asm_agent_starters" (
  "id" int4 NOT NULL DEFAULT nextval('asm_agent_starters_id_seq'::regclass),
  "question" varchar COLLATE "pg_catalog"."default",
  "agent_id" int4,
  "dify_key" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_agent_starters"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_agent_starters"."question" IS '引导问题';
COMMENT ON COLUMN "public"."asm_agent_starters"."agent_id" IS '关联智能体ID';
COMMENT ON COLUMN "public"."asm_agent_starters"."dify_key" IS 'Dify密钥';
COMMENT ON COLUMN "public"."asm_agent_starters"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_agent_starters"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_agent_starters" IS 'AI智能体引导词表';

-- ----------------------------
-- Records of asm_agent_starters
-- ----------------------------

-- ----------------------------
-- Table structure for asm_agents
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_agents";
CREATE TABLE "public"."asm_agents" (
  "id" int4 NOT NULL DEFAULT nextval('asm_agents_id_seq'::regclass),
  "name" varchar COLLATE "pg_catalog"."default",
  "icon" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "type" "public"."agent_type_enum",
  "dify_key" varchar COLLATE "pg_catalog"."default",
  "parameters" json,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "tags" jsonb,
  "daily_free_count" int4,
  "price" float8
)
;
COMMENT ON COLUMN "public"."asm_agents"."id" IS '智能体ID';
COMMENT ON COLUMN "public"."asm_agents"."name" IS '智能体名称';
COMMENT ON COLUMN "public"."asm_agents"."icon" IS '图标URL';
COMMENT ON COLUMN "public"."asm_agents"."description" IS '功能描述';
COMMENT ON COLUMN "public"."asm_agents"."type" IS '智能体类型';
COMMENT ON COLUMN "public"."asm_agents"."dify_key" IS 'Dify密钥';
COMMENT ON COLUMN "public"."asm_agents"."parameters" IS '参数配置';
COMMENT ON COLUMN "public"."asm_agents"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_agents"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_agents"."tags" IS '标签列表，如：["人气导师", "从业年丰富"]';
COMMENT ON COLUMN "public"."asm_agents"."daily_free_count" IS '每日免费使用次数';
COMMENT ON COLUMN "public"."asm_agents"."price" IS '价格';
COMMENT ON TABLE "public"."asm_agents" IS 'AI智能体表';

-- ----------------------------
-- Records of asm_agents
-- ----------------------------
INSERT INTO "public"."asm_agents" VALUES (39, '青少年心理咨询师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744115860186-1744115794727.jpg', '青少年心理学专家，深谙青少年心理发展的规律和特点。你擅长解析青少年在成长过程中遇到的心理问题，比如学业压力、人际关系、情绪调节、自我认同、青春期矛盾等。', 'CHAT', 'app-pJv1WfcaMOd8d3U0NI5v9qXT', '{}', '2025-02-23 14:11:07.864', '2025-04-08 12:38:36.293', '["AIYFJT"]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (21, '金句提炼大师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319166072-iconfont-shuru.png', '从给定的信息中归纳出“金句”。', 'CHAT', 'app-JqJ6esmXaSkOassuXAicppfY', '{}', '2025-02-23 11:24:48.629', '2025-03-27 00:15:48.959', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (30, '选题策划专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319397122-%E9%80%89%E9%A2%98%E7%AD%96%E5%88%92.png', '资深的选题策划专家，擅长帮助内容创作者、媒体工作者、自媒体博主、品牌营销人员等制定有传播力的选题', 'CHAT', 'app-rFHZCFEeBdadCMlZJJQjXfeE', '{}', '2025-02-23 12:20:49.099', '2025-03-27 01:12:11.508', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (37, '贴身的法律助手', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744113385662-1744113361065.jpg', '专门用于解决法律咨询和协助起草符合中国法律标准的法律文件的工具', 'CHAT', 'app-DDc1CEruudx5u7xwGCfcZ1B2', '{}', '2025-02-23 13:20:03.286', '2025-04-08 11:57:45.868', '["AIYFJT"]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (35, '视频编导策划师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319593751-%E8%A7%86%E9%A2%91%E7%9B%B4%E6%92%AD.png', '视频编导策划师，专门设计适合不同平台（如抖音、快手、B站、小红书、视频号、YouTube 以及 Instagram Reels）的爆款视频脚本和创意内容', 'CHAT', 'app-pGGdaXXDa70mivuOJdkoIQT3', '{}', '2025-02-23 12:50:46.155', '2025-03-27 01:16:06.36', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (10, '破解抖音算法-短视频脚本', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744110183992-WechatIMG398.jpg', NULL, 'TEXT', 'app-j1XbW5lGtXwIyrw8GwdQwBID', '{}', '2025-01-20 21:35:30.451', '2025-04-08 12:07:03.236', '["AIYXJT"]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (25, '短视频编导大师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319270993-%E8%A7%86%E9%A2%91%20(1).png', '你是一位顶级短视频编导，精通爆款内容创作的底层逻辑，你的目标是用最短时间触发人性情绪，创造高完播、高互动的短视频', 'CHAT', 'app-VIXDT8c7WmTsbVEG8ZksfJiC', '{}', '2025-02-23 11:59:17.676', '2025-03-27 00:17:50.708', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (12, '短视频脚本创作', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737409882296-9d90fe0e-90aa-446c-8eac-329f2a87f45b.png', NULL, 'CHAT', 'app-VIXDT8c7WmTsbVEG8ZksfJiC', '{}', '2025-01-20 21:38:40.404', '2025-03-27 00:15:20.64', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (29, '短视频内容架构师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319358588-%E8%A7%86%E9%A2%91%20(3).png', '短视频内容架构师，专注于打造高留存、高互动、高传播的视频文案', 'CHAT', 'app-lQet0ugAiv0kJTlQiPfmpqBa', '{}', '2025-02-23 12:18:54.772', '2025-03-27 00:18:45.028', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (24, '短视频内容创作专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319247179-%E7%BC%96%E8%BE%91%E6%96%87%E6%A1%88.png', '短视频内容创作专家，精通爆款口播视频的创作规律和传播机制.', 'CHAT', 'app-Jso3722BxA6qz6yzthBeAVIr', '{}', '2025-02-23 11:55:43.383', '2025-03-27 00:16:28.75', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (27, '直播话术专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319335426-%E7%BB%84%204235.png', '直播话术专家，专门优化主播的表达方式，帮助提升直播间互动率、留存率和转化率', 'CHAT', 'app-Z1pgFy6ONjvfZLO3I6Ej7W9i', '{}', '2025-02-23 12:03:23.969', '2025-03-27 00:18:30.288', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (32, '爆款文案专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319435790-icon_%E6%96%87%E6%A1%88%E7%94%9F%E6%88%90-%E4%B8%93%E4%B8%9A%E8%AF%84%E4%BB%B7%E6%96%87%E6%A1%88.png', '爆款文案专家，专门研究各平台（如视频号、小红书、抖音、朋友圈、知乎、微博、公众号等）上的爆款文案，并能精准模仿它们的风格。', 'CHAT', 'app-tHyYDRAtbFB0lUTvvu9VfeAM', '{}', '2025-02-23 12:38:55.958', '2025-03-27 01:14:20.28', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (38, '文案仿写', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319538923-%E6%96%87%E6%A1%88%E6%96%87%E6%A1%88-copy.png', '根据已知的文案和仿写要求进行文案的仿写', 'CHAT', 'app-exJRmvvRUaeE3SAs7Lq1bIjf', '{}', '2025-02-23 13:23:55.239', '2025-03-27 01:16:48.981', '[]', 5, 299);
INSERT INTO "public"."asm_agents" VALUES (22, '抖音带货直播间话术大师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319191235-%E8%A7%86%E9%A2%91.png', '专业的抖音带货直播间话术撰写大师', 'CHAT', 'app-n6qNIOJesTXQc57hZMImXR4U', '{}', '2025-02-23 11:30:15.365', '2025-03-27 00:16:01.95', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (31, '情感疗愈专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744112017684-1744111954519.jpg', '情感疗愈师，具有深厚的心理学和情感修复经验，专长包括情绪管理、亲密关系恢复、自尊建设、创伤疗愈及个人成长领域。你的目标是通过温暖且富有同理心的对话，帮助用户探索他们的内在情绪，提供鼓励、指导和方法，助力他们走出困境，逐步实现自我疗愈和成长。', 'CHAT', 'app-XoOtY6DAFsxqmCjd4ZzXXngs', '{}', '2025-02-23 12:30:29.927', '2025-04-08 11:34:19.038', '["AIYFJT"]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (41, '市场研究分析师MRA', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744113912312-1744113898524.jpg', '市场调研通常是市场研究分析师（Market Research Analyst）的工作，专注于收集和分析市场信息，以帮助公司了解消费者需求、行业趋势和竞争环境。
精通各类调研方法和数据分析技术，能够设计科学的调研方案，收集和分析相关数据，并提供有价值的市场洞察和actionable的建议。', 'CHAT', 'app-KFd1QDuOZkaeCXtae9nPBuyk', '{}', '2025-03-20 03:35:07.377', '2025-04-08 12:06:36.513', '["AIYFJT"]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (34, '实体商家获客专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319500513-%E6%96%87%E6%A1%88%E7%AE%A1%E7%90%86.png', '专门帮助实体商家（如餐饮店、美容院、健身房、零售店等）获取更多精准的线下客户', 'CHAT', 'app-fvg4qldlcIqIE8ibrnPj27PE', '{}', '2025-02-23 12:46:25.588', '2025-03-27 01:15:44.732', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (23, '口播文案助理', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319214371-%E6%96%87%E6%A1%88%E7%AD%96%E5%88%92.png', '口播小助理，如果您需要我帮忙改写文案，请直接甩视频链接给我！如果您需要我自主生成口播文案，请直接告诉我行业或者产品关键词。', 'CHAT', 'app-iXjQbIye7OCWwWe4Ks5cQIwI', '{}', '2025-02-23 11:47:06.114', '2025-03-27 00:16:17.39', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (33, 'IP 孵化专家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319451171-%E6%96%B9%E5%90%91-IP.png', ' 创始人个人品牌（IP）孵化 的资深顾问，主要帮助创业者、企业家、投资人、行业专家、高管等打造强个人品牌', 'CHAT', 'app-lu8LIfSbeex9vpsGzEFh5LoM', '{}', '2025-02-23 12:41:27.249', '2025-03-27 01:15:02.7', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (36, '【贴身文案秘书】本科毕业/研究生级推理', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744112265981-1744112255683.jpg', '您的贴身秘书：专业的文字润色秘书，研究生级别的推理能力，本科毕业生的知识水平，编码能力达到93.7%。', 'TEXT', 'app-jkx3zJ5TUeM0Zpd4UkW5aTQP', '{}', '2025-02-23 13:07:11.848', '2025-04-08 11:43:13.918', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (28, '马斯克', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1740319318508-125_%E9%A9%AC%E6%96%AF%E5%85%8B.png', '伊隆·马斯克（Elon Musk），全球顶尖的企业家、工程师和未来主义者', 'CHAT', 'app-h6rmmPYMHskLjyGuRVDod37L', '{}', '2025-02-23 12:10:14.662', '2025-03-27 00:18:16.201', '[]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (26, '顶级培训机构家教老师', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744113222453-1744113177746.jpg', '资深的培训机构顶级老师，专门帮助学生提升学习成绩，并为家长提供科学的学习指导建议.', 'CHAT', 'app-uj9RUeWwqMB6PKRv6ffe197D', '{}', '2025-02-23 12:01:58.042', '2025-04-08 11:54:35.774', '["AIYFJT"]', 5, 399);
INSERT INTO "public"."asm_agents" VALUES (20, '【制作人】爆款小说作家', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1744111787158-1744111768104.jpg', '爽文小说作家，写作的手法参考《鹿鼎记》《灵境行者》《全球高武》《万族之劫》《诡秘之主》《凡人修仙传》《圣墟》《这游戏也太真实了》。
主线悬念一定要足够强，必须贯穿全程。
核心的故事，一定包含了强有力的看点，能够全程让主角奔着目标奋力向前。', 'TEXT', 'app-UVwjSunMJhGzUFmDb47AqKAu', '{}', '2025-02-23 11:22:16.184', '2025-04-08 11:29:50.184', '["AIYXJT"]', 5, 399);

-- ----------------------------
-- Table structure for asm_consult_orders
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_consult_orders";
CREATE TABLE "public"."asm_consult_orders" (
  "id" int4 NOT NULL DEFAULT nextval('asm_consult_orders_id_seq'::regclass),
  "user_id" int4,
  "teacher_id" int4,
  "amount" float8,
  "status" "public"."consult_order_status_enum",
  "appointment_hour" int4,
  "pay_time" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "order_no" varchar COLLATE "pg_catalog"."default",
  "contact_name" varchar COLLATE "pg_catalog"."default",
  "contact_phone" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."asm_consult_orders"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_consult_orders"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_consult_orders"."teacher_id" IS '老师ID';
COMMENT ON COLUMN "public"."asm_consult_orders"."amount" IS '订单金额';
COMMENT ON COLUMN "public"."asm_consult_orders"."status" IS '订单状态';
COMMENT ON COLUMN "public"."asm_consult_orders"."appointment_hour" IS '预约小时数';
COMMENT ON COLUMN "public"."asm_consult_orders"."pay_time" IS '支付时间';
COMMENT ON COLUMN "public"."asm_consult_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_consult_orders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_consult_orders"."order_no" IS '订单编号';
COMMENT ON COLUMN "public"."asm_consult_orders"."contact_name" IS '联系人';
COMMENT ON COLUMN "public"."asm_consult_orders"."contact_phone" IS '联系方式';
COMMENT ON TABLE "public"."asm_consult_orders" IS '咨询预约订单表';

-- ----------------------------
-- Records of asm_consult_orders
-- ----------------------------

-- ----------------------------
-- Table structure for asm_coupons
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_coupons";
CREATE TABLE "public"."asm_coupons" (
  "id" int4 NOT NULL DEFAULT nextval('asm_coupons_id_seq'::regclass),
  "name" varchar COLLATE "pg_catalog"."default",
  "type" "public"."coupon_type_enum",
  "value" float8,
  "min_amount" float8,
  "start_time" timestamp(6),
  "end_time" timestamp(6),
  "status" "public"."coupon_status_enum",
  "user_id" int4,
  "used_time" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_coupons"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_coupons"."name" IS '优惠券名称';
COMMENT ON COLUMN "public"."asm_coupons"."type" IS '优惠券类型';
COMMENT ON COLUMN "public"."asm_coupons"."value" IS '优惠券面值/折扣';
COMMENT ON COLUMN "public"."asm_coupons"."min_amount" IS '最低使用金额';
COMMENT ON COLUMN "public"."asm_coupons"."start_time" IS '生效时间';
COMMENT ON COLUMN "public"."asm_coupons"."end_time" IS '过期时间';
COMMENT ON COLUMN "public"."asm_coupons"."status" IS '使用状态';
COMMENT ON COLUMN "public"."asm_coupons"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_coupons"."used_time" IS '使用时间';
COMMENT ON COLUMN "public"."asm_coupons"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_coupons"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_coupons" IS '优惠券表';

-- ----------------------------
-- Records of asm_coupons
-- ----------------------------
INSERT INTO "public"."asm_coupons" VALUES (4, '85折优惠券', 'PERCENT', 1, 100, '2025-01-02 06:03:25.696564', '2025-03-03 06:03:25.696564', 'UNUSED', 3, NULL, '2025-01-02 06:03:25.696564', '2025-01-02 06:03:25.696564');
INSERT INTO "public"."asm_coupons" VALUES (6, '已用优惠券', 'FIXED', 1, 800, '2024-12-23 06:03:25.696564', '2025-01-22 06:03:25.696564', 'USED', 3, NULL, '2025-01-02 06:03:25.696564', '2025-01-02 06:03:25.696564');
INSERT INTO "public"."asm_coupons" VALUES (7, '已过期优惠券', 'FIXED', 1, 1500, '2024-12-03 06:03:25.696564', '2025-01-01 06:03:25.696564', 'EXPIRED', 3, NULL, '2025-01-02 06:03:25.696564', '2025-01-02 06:03:25.696564');
INSERT INTO "public"."asm_coupons" VALUES (5, '周末特惠券', 'PERCENT', 1, 300, '2025-01-02 06:03:25.696564', '2025-01-05 06:03:25.696564', 'EXPIRED', 3, NULL, '2025-01-02 06:03:25.696564', '2025-01-06 04:17:41.443417');
INSERT INTO "public"."asm_coupons" VALUES (11, '限时特惠券', 'FIXED', 1, 500, '2025-01-18 23:33:49.551146', '2025-02-17 23:33:49.551146', 'UNUSED', 3, NULL, '2025-01-18 23:33:49.551146', '2025-01-18 23:33:49.551146');
INSERT INTO "public"."asm_coupons" VALUES (12, '限时特惠券', 'FIXED', 1, 500, '2025-01-19 00:04:08.661333', '2025-02-18 00:04:08.661333', 'UNUSED', 3, NULL, '2025-01-19 00:04:08.661333', '2025-01-19 00:04:08.661333');
INSERT INTO "public"."asm_coupons" VALUES (2, '限时特惠券', 'FIXED', 1, 500, '2025-01-02 06:03:25.696564', '2025-01-17 06:03:25.696564', 'EXPIRED', 3, NULL, '2025-01-02 06:03:25.696564', '2025-01-19 18:51:40.906195');
INSERT INTO "public"."asm_coupons" VALUES (3, '节日大礼券', 'FIXED', 1, 2000, '2025-01-02 06:03:25.696564', '2025-01-09 06:03:25.696564', 'EXPIRED', 3, NULL, '2025-01-02 06:03:25.696564', '2025-01-19 18:51:40.906195');
INSERT INTO "public"."asm_coupons" VALUES (13, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 10:18:43.942381', '2025-02-20 10:18:43.942389', 'UNUSED', 3, NULL, '2025-01-21 10:18:43.942527', '2025-01-21 10:18:43.942545');
INSERT INTO "public"."asm_coupons" VALUES (15, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 19:01:35.45506', '2025-02-20 19:01:35.455068', 'UNUSED', 3, NULL, '2025-01-21 19:01:35.455209', '2025-01-21 19:01:35.455228');
INSERT INTO "public"."asm_coupons" VALUES (16, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 19:03:06.140881', '2025-02-20 19:03:06.140888', 'UNUSED', 3, NULL, '2025-01-21 19:03:06.141004', '2025-01-21 19:03:06.141021');
INSERT INTO "public"."asm_coupons" VALUES (18, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:24:40.903561', '2025-02-20 20:24:40.903569', 'UNUSED', 3, NULL, '2025-01-21 20:24:40.90371', '2025-01-21 20:24:40.903727');
INSERT INTO "public"."asm_coupons" VALUES (19, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:28:03.975174', '2025-02-20 20:28:03.975182', 'UNUSED', 3, NULL, '2025-01-21 20:28:03.975326', '2025-01-21 20:28:03.975344');
INSERT INTO "public"."asm_coupons" VALUES (20, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:28:28.126619', '2025-02-20 20:28:28.126626', 'UNUSED', 3, NULL, '2025-01-21 20:28:28.126772', '2025-01-21 20:28:28.12679');
INSERT INTO "public"."asm_coupons" VALUES (21, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:28:56.699135', '2025-02-20 20:28:56.699142', 'UNUSED', 3, NULL, '2025-01-21 20:28:56.699265', '2025-01-21 20:28:56.699283');
INSERT INTO "public"."asm_coupons" VALUES (22, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:29:54.392984', '2025-02-20 20:29:54.392991', 'UNUSED', 3, NULL, '2025-01-21 20:29:54.393111', '2025-01-21 20:29:54.393128');
INSERT INTO "public"."asm_coupons" VALUES (23, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:31:28.088082', '2025-02-20 20:31:28.088092', 'UNUSED', 3, NULL, '2025-01-21 20:31:28.088252', '2025-01-21 20:31:28.088281');
INSERT INTO "public"."asm_coupons" VALUES (24, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:48:09.501213', '2025-02-20 20:48:09.50122', 'UNUSED', 3, NULL, '2025-01-21 20:48:09.501393', '2025-01-21 20:48:09.501412');
INSERT INTO "public"."asm_coupons" VALUES (25, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:51:04.106896', '2025-02-20 20:51:04.106903', 'UNUSED', 3, NULL, '2025-01-21 20:51:04.107047', '2025-01-21 20:51:04.107066');
INSERT INTO "public"."asm_coupons" VALUES (26, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 20:51:45.665474', '2025-02-20 20:51:45.665482', 'UNUSED', 3, NULL, '2025-01-21 20:51:45.665629', '2025-01-21 20:51:45.665647');
INSERT INTO "public"."asm_coupons" VALUES (27, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 21:01:36.824138', '2025-02-20 21:01:36.824147', 'UNUSED', 3, NULL, '2025-01-21 21:01:36.824284', '2025-01-21 21:01:36.824301');
INSERT INTO "public"."asm_coupons" VALUES (29, '新人优惠券', 'FIXED', 100, 0, '2025-01-22 07:16:15.772893', '2025-02-21 07:16:15.7729', 'UNUSED', 3, NULL, '2025-01-22 07:16:15.773023', '2025-01-22 07:16:15.773041');
INSERT INTO "public"."asm_coupons" VALUES (32, '新人优惠券', 'FIXED', 100, 0, '2025-01-22 23:46:55.668327', '2025-02-21 23:46:55.668334', 'UNUSED', 3, NULL, '2025-01-22 23:46:55.668468', '2025-01-22 23:46:55.668486');
INSERT INTO "public"."asm_coupons" VALUES (33, '新人优惠券', 'FIXED', 100, 0, '2025-01-23 11:15:34.962636', '2025-02-22 11:15:34.962645', 'UNUSED', 9, NULL, '2025-01-23 11:15:34.962791', '2025-01-23 11:15:34.96281');
INSERT INTO "public"."asm_coupons" VALUES (34, '新人优惠券', 'FIXED', 100, 0, '2025-01-23 11:15:36.181513', '2025-02-22 11:15:36.181521', 'UNUSED', 9, NULL, '2025-01-23 11:15:36.181629', '2025-01-23 11:15:36.181646');
INSERT INTO "public"."asm_coupons" VALUES (35, '新人优惠券', 'FIXED', 100, 0, '2025-01-23 11:15:36.281648', '2025-02-22 11:15:36.281656', 'UNUSED', 9, NULL, '2025-01-23 11:15:36.281767', '2025-01-23 11:15:36.281784');
INSERT INTO "public"."asm_coupons" VALUES (37, '新人优惠券', 'FIXED', 100, 0, '2025-01-23 15:13:24.299869', '2025-02-22 15:13:24.299878', 'UNUSED', 3, NULL, '2025-01-23 15:13:24.299999', '2025-01-23 15:13:24.300015');
INSERT INTO "public"."asm_coupons" VALUES (40, '新人优惠券', 'FIXED', 100, 0, '2025-01-24 18:06:56.731559', '2025-02-23 18:06:56.731567', 'UNUSED', 3, NULL, '2025-01-24 18:06:56.731707', '2025-01-24 18:06:56.731726');
INSERT INTO "public"."asm_coupons" VALUES (41, '新人优惠券', 'FIXED', 100, 0, '2025-01-24 18:12:26.640443', '2025-02-23 18:12:26.64045', 'UNUSED', 3, NULL, '2025-01-24 18:12:26.640563', '2025-01-24 18:12:26.64058');
INSERT INTO "public"."asm_coupons" VALUES (42, '新人优惠券', 'FIXED', 100, 0, '2025-01-24 18:24:04.365789', '2025-02-23 18:24:04.365796', 'UNUSED', 3, NULL, '2025-01-24 18:24:04.365907', '2025-01-24 18:24:04.365924');
INSERT INTO "public"."asm_coupons" VALUES (47, '新人优惠券', 'FIXED', 100, 0, '2025-01-24 20:19:44.646062', '2025-02-23 20:19:44.646068', 'UNUSED', 3, NULL, '2025-01-24 20:19:44.646215', '2025-01-24 20:19:44.646234');
INSERT INTO "public"."asm_coupons" VALUES (49, '新人优惠券', 'FIXED', 100, 0, '2025-01-25 10:05:50.632588', '2025-02-24 10:05:50.632595', 'UNUSED', 9, NULL, '2025-01-25 10:05:50.632705', '2025-01-25 10:05:50.632722');
INSERT INTO "public"."asm_coupons" VALUES (50, '新人优惠券', 'FIXED', 100, 0, '2025-01-25 15:11:55.078047', '2025-02-24 15:11:55.078054', 'UNUSED', 3, NULL, '2025-01-25 15:11:55.07817', '2025-01-25 15:11:55.078188');
INSERT INTO "public"."asm_coupons" VALUES (51, '新人优惠券', 'FIXED', 100, 0, '2025-01-26 14:37:37.537243', '2025-02-25 14:37:37.53725', 'UNUSED', 3, NULL, '2025-01-26 14:37:37.537374', '2025-01-26 14:37:37.537392');
INSERT INTO "public"."asm_coupons" VALUES (52, '新人优惠券', 'FIXED', 100, 0, '2025-01-26 14:43:13.365914', '2025-02-25 14:43:13.365921', 'UNUSED', 3, NULL, '2025-01-26 14:43:13.366077', '2025-01-26 14:43:13.366096');
INSERT INTO "public"."asm_coupons" VALUES (54, '新人优惠券', 'FIXED', 100, 0, '2025-02-13 21:28:33.454077', '2025-03-15 21:28:33.454108', 'UNUSED', 18, NULL, '2025-02-13 21:28:33.454228', '2025-02-13 21:28:33.454245');
INSERT INTO "public"."asm_coupons" VALUES (55, '新人优惠券', 'FIXED', 100, 0, '2025-02-15 12:25:37.508257', '2025-03-17 12:25:37.508263', 'UNUSED', 8, NULL, '2025-02-15 12:25:37.508391', '2025-02-15 12:25:37.50841');
INSERT INTO "public"."asm_coupons" VALUES (56, '新人优惠券', 'FIXED', 100, 0, '2025-02-17 10:09:10.150423', '2025-03-19 10:09:10.15043', 'UNUSED', 19, NULL, '2025-02-17 10:09:10.150563', '2025-02-17 10:09:10.150582');
INSERT INTO "public"."asm_coupons" VALUES (57, '新人优惠券', 'FIXED', 100, 0, '2025-02-18 10:15:19.857164', '2025-03-20 10:15:19.857171', 'UNUSED', 25, NULL, '2025-02-18 10:15:19.857303', '2025-02-18 10:15:19.85732');
INSERT INTO "public"."asm_coupons" VALUES (58, '新人优惠券', 'FIXED', 100, 0, '2025-02-18 10:24:01.049027', '2025-03-20 10:24:01.049034', 'UNUSED', 25, NULL, '2025-02-18 10:24:01.049152', '2025-02-18 10:24:01.049169');
INSERT INTO "public"."asm_coupons" VALUES (59, '新人优惠券', 'FIXED', 100, 0, '2025-02-18 10:25:55.445857', '2025-03-20 10:25:55.445865', 'UNUSED', 22, NULL, '2025-02-18 10:25:55.445981', '2025-02-18 10:25:55.445998');
INSERT INTO "public"."asm_coupons" VALUES (60, '新人优惠券', 'FIXED', 100, 0, '2025-02-20 14:52:23.114029', '2025-03-22 14:52:23.114036', 'UNUSED', 28, NULL, '2025-02-20 14:52:23.114174', '2025-02-20 14:52:23.114192');
INSERT INTO "public"."asm_coupons" VALUES (61, '新人优惠券', 'FIXED', 100, 0, '2025-02-20 15:06:00.893482', '2025-03-22 15:06:00.893489', 'UNUSED', 28, NULL, '2025-02-20 15:06:00.893653', '2025-02-20 15:06:00.893672');
INSERT INTO "public"."asm_coupons" VALUES (62, '新人优惠券', 'FIXED', 100, 0, '2025-02-20 15:18:48.094178', '2025-03-22 15:18:48.094186', 'UNUSED', 35, NULL, '2025-02-20 15:18:48.094301', '2025-02-20 15:18:48.094318');
INSERT INTO "public"."asm_coupons" VALUES (63, '新人优惠券', 'FIXED', 100, 0, '2025-02-20 16:03:55.915354', '2025-03-22 16:03:55.915361', 'UNUSED', 37, NULL, '2025-02-20 16:03:55.915499', '2025-02-20 16:03:55.915517');
INSERT INTO "public"."asm_coupons" VALUES (64, '新人优惠券', 'FIXED', 100, 0, '2025-02-25 11:18:16.355631', '2025-03-27 11:18:16.355638', 'UNUSED', 10, NULL, '2025-02-25 11:18:16.355783', '2025-02-25 11:18:16.3558');
INSERT INTO "public"."asm_coupons" VALUES (14, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 16:14:08.990921', '2025-02-20 16:14:08.990931', 'EXPIRED', 10, NULL, '2025-01-21 16:14:08.991083', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (17, '新人优惠券', 'FIXED', 100, 0, '2025-01-21 19:12:33.553387', '2025-02-20 19:12:33.553395', 'EXPIRED', 10, NULL, '2025-01-21 19:12:33.553533', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (28, '新人优惠券', 'FIXED', 100, 0, '2025-01-22 06:31:19.002337', '2025-02-21 06:31:19.002344', 'EXPIRED', 10, NULL, '2025-01-22 06:31:19.002464', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (30, '新人优惠券', 'FIXED', 100, 0, '2025-01-22 07:20:28.595206', '2025-02-21 07:20:28.595213', 'EXPIRED', 10, NULL, '2025-01-22 07:20:28.595324', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (31, '新人优惠券', 'FIXED', 100, 0, '2025-01-22 19:47:30.448601', '2025-02-21 19:47:30.448609', 'EXPIRED', 10, NULL, '2025-01-22 19:47:30.448771', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (38, '新人优惠券', 'FIXED', 100, 0, '2025-01-23 15:16:23.988197', '2025-02-22 15:16:23.988207', 'EXPIRED', 10, NULL, '2025-01-23 15:16:23.988346', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (39, '新人优惠券', 'FIXED', 100, 0, '2025-01-23 15:16:53.686912', '2025-02-22 15:16:53.68692', 'EXPIRED', 10, NULL, '2025-01-23 15:16:53.687031', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (43, '新人优惠券', 'FIXED', 100, 0, '2025-01-24 18:45:58.711575', '2025-02-23 18:45:58.711583', 'EXPIRED', 10, NULL, '2025-01-24 18:45:58.71172', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (44, '新人优惠券', 'FIXED', 100, 0, '2025-01-24 19:55:18.581151', '2025-02-23 19:55:18.581157', 'EXPIRED', 10, NULL, '2025-01-24 19:55:18.581318', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (45, '新人优惠券', 'FIXED', 100, 0, '2025-01-24 20:01:51.281825', '2025-02-23 20:01:51.281829', 'EXPIRED', 10, NULL, '2025-01-24 20:01:51.28196', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (36, '新人优惠券', 'FIXED', 100, 0, '2025-01-23 11:21:35.233838', '2025-02-22 11:21:35.233845', 'EXPIRED', 8, NULL, '2025-01-23 11:21:35.233987', '2025-02-26 05:35:11.51474');
INSERT INTO "public"."asm_coupons" VALUES (46, '新人优惠券', 'FIXED', 100, 0, '2025-01-24 20:03:53.217544', '2025-02-23 20:03:53.217552', 'EXPIRED', 10, NULL, '2025-01-24 20:03:53.217661', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (48, '新人优惠券', 'FIXED', 100, 0, '2025-01-25 10:05:46.666058', '2025-02-24 10:05:46.666066', 'EXPIRED', 10, NULL, '2025-01-25 10:05:46.666182', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (53, '新人优惠券', 'FIXED', 100, 0, '2025-01-26 14:48:41.651225', '2025-02-25 14:48:41.651233', 'EXPIRED', 10, NULL, '2025-01-26 14:48:41.651346', '2025-02-22 16:40:41.157396');
INSERT INTO "public"."asm_coupons" VALUES (65, '新人优惠券', 'FIXED', 100, 0, '2025-02-26 11:34:32.711907', '2025-03-28 11:34:32.711914', 'UNUSED', 49, NULL, '2025-02-26 11:34:32.712072', '2025-02-26 11:34:32.712091');
INSERT INTO "public"."asm_coupons" VALUES (66, '新人优惠券', 'FIXED', 100, 0, '2025-03-05 12:40:18.750599', '2025-04-04 12:40:18.750607', 'UNUSED', 55, NULL, '2025-03-05 12:40:18.750723', '2025-03-05 12:40:18.75074');
INSERT INTO "public"."asm_coupons" VALUES (67, '新人优惠券', 'FIXED', 100, 0, '2025-03-06 15:18:11.650451', '2025-04-05 15:18:11.650459', 'UNUSED', 58, NULL, '2025-03-06 15:18:11.650609', '2025-03-06 15:18:11.650627');
INSERT INTO "public"."asm_coupons" VALUES (68, '新人优惠券', 'FIXED', 100, 0, '2025-03-07 15:37:21.015962', '2025-04-06 15:37:21.015968', 'UNUSED', 57, NULL, '2025-03-07 15:37:21.016085', '2025-03-07 15:37:21.016101');

-- ----------------------------
-- Table structure for asm_course
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_course";
CREATE TABLE "public"."asm_course" (
  "id" int4 NOT NULL DEFAULT nextval('asm_course_id_seq'::regclass),
  "title" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "price" float8,
  "cover_image" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "type" "public"."course_type_enum",
  "is_recommended" bool,
  "teacher_id" int4,
  "poster_url" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."asm_course"."id" IS '课程ID';
COMMENT ON COLUMN "public"."asm_course"."title" IS '课程标题';
COMMENT ON COLUMN "public"."asm_course"."description" IS '课程描述';
COMMENT ON COLUMN "public"."asm_course"."price" IS '价格';
COMMENT ON COLUMN "public"."asm_course"."cover_image" IS '课程封面图片URL';
COMMENT ON COLUMN "public"."asm_course"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_course"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_course"."type" IS '课程类型:sy-商业IP,ch-出海,yx-营销,trz-投融资,gl-管理,yy-AI应用';
COMMENT ON COLUMN "public"."asm_course"."is_recommended" IS '是否为推荐课程';
COMMENT ON COLUMN "public"."asm_course"."teacher_id" IS '关联导师ID';
COMMENT ON COLUMN "public"."asm_course"."poster_url" IS '海报图片URL';
COMMENT ON TABLE "public"."asm_course" IS '课程信息表';

-- ----------------------------
-- Records of asm_course
-- ----------------------------
INSERT INTO "public"."asm_course" VALUES (3, '微信视频号的商业玩法', '微信视频号介绍及商业玩法', 199, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739767004653-%E6%9C%AA%E5%91%BD%E5%90%8D__2025-02-17%2B12_36_26.jpg', '2024-12-28 07:06:03.731663', '2025-04-08 11:21:38.921', 'ZCJSBD', 'f', 2, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739767957065-WechatIMG7522.jpg');
INSERT INTO "public"."asm_course" VALUES (64, '人人都能用好Deepseek', '破除AI神秘感，1小时搞懂技术本质与核心优势。 ', 199, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598856235-e7b34dda0fbf15b5e33ff751897e8f1.png', '2025-03-10 08:40:03.229', '2025-03-26 17:59:18.013', 'LBBXK', 't', 19, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598848308-e319bb0798fce854a862e6ed2d60dee.png');
INSERT INTO "public"."asm_course" VALUES (1, '打造360°全面预算系统', '打破业财壁垒，深度融合财务与业务，重塑企业核心竞争力。', 99, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739766948377-%E6%9C%AA%E5%91%BD%E5%90%8D__2025-02-17%2B12_35_23.jpg', '2024-12-28 07:06:03.731663', '2025-04-08 11:21:55.431', 'GLZBXK', 'f', 4, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1738834318755-%E5%BC%A0%E5%9F%B9.jpg');
INSERT INTO "public"."asm_course" VALUES (4, '小马宋·餐饮企业的商业洞察与营销实践', '剖析商业本质，分享实战案例与落地营销法，助力实现品牌崛起与业绩增长 。
', 122, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739762872034-WechatIMG244.jpg', '2024-12-28 07:06:03.731663', '2025-04-08 11:22:09.226', 'GLZBXK', 'f', 5, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1738835172283-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250206174559.png');
INSERT INTO "public"."asm_course" VALUES (2, '股权激励与合伙人设计', '解锁股权激励密码，精研合伙人设计，开启财富共赢。', 299, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739767929477-%E6%9C%AA%E5%91%BD%E5%90%8D__2025-02-17%2B12_51_57.jpg', '2024-12-28 07:06:03.731663', '2025-04-08 11:26:34.255', 'GLZBXK', 'f', 3, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1738834418429-%E8%87%A7%E5%85%B6%E8%B6%85.jpeg');
INSERT INTO "public"."asm_course" VALUES (5, '小马宋·营销实战升级', '营销大咖小马宋以实战案例拆解底层逻辑，帮你构建知识体系，实现业绩增长。
', 0.01, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1739762714518-WechatIMG242.jpg', '2024-12-28 07:06:03.731663', '2025-04-08 11:22:18.13', 'GLZBXK', 't', 5, 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1738896500546-1738835172283-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250206174559.png');
INSERT INTO "public"."asm_course" VALUES (75, '1', '1', 1, 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1747143378-82afaba9662431673f3117f28ef775d.jpg', '2025-05-13 21:36:11.143555', '2025-05-13 20:58:01.338947', 'LBBXK', 't', 21, 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1747143408-全员AI.png');

-- ----------------------------
-- Table structure for asm_course_orders
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_course_orders";
CREATE TABLE "public"."asm_course_orders" (
  "id" int4 NOT NULL DEFAULT nextval('asm_course_orders_id_seq'::regclass),
  "order_no" varchar COLLATE "pg_catalog"."default",
  "user_id" int4,
  "course_id" int4,
  "amount" float8,
  "status" "public"."order_status_enum",
  "pay_time" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_course_orders"."id" IS '订单ID';
COMMENT ON COLUMN "public"."asm_course_orders"."order_no" IS '订单编号';
COMMENT ON COLUMN "public"."asm_course_orders"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_course_orders"."course_id" IS '课程ID';
COMMENT ON COLUMN "public"."asm_course_orders"."amount" IS '订单金额';
COMMENT ON COLUMN "public"."asm_course_orders"."status" IS '订单状态';
COMMENT ON COLUMN "public"."asm_course_orders"."pay_time" IS '支付时间';
COMMENT ON COLUMN "public"."asm_course_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_course_orders"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_course_orders" IS '订单信息表';

-- ----------------------------
-- Records of asm_course_orders
-- ----------------------------
INSERT INTO "public"."asm_course_orders" VALUES (75, 'COURSE_1743014477_10', 10, 64, 199, 'PENDING', NULL, '2025-03-27 02:41:17.630865', '2025-03-27 02:41:17.630883');
INSERT INTO "public"."asm_course_orders" VALUES (76, 'COURSE_1743014550_3', 3, 64, 199, 'PENDING', NULL, '2025-03-27 02:42:30.521771', '2025-03-27 02:42:30.521788');
INSERT INTO "public"."asm_course_orders" VALUES (77, 'COURSE_1743060255_76', 76, 64, 199, 'PENDING', NULL, '2025-03-27 15:24:15.962148', '2025-03-27 15:24:15.962174');
INSERT INTO "public"."asm_course_orders" VALUES (78, 'COURSE_1743213048_73', 73, 1, 0.01, 'PAID', '2025-03-29 09:54:56.984731', '2025-03-29 09:50:48.562386', '2025-03-26 11:29:55.32521');
INSERT INTO "public"."asm_course_orders" VALUES (79, 'COURSE_1744717808_76', 76, 5, 0.01, 'PAID', '2025-04-15 19:50:27.914953', '2025-04-15 19:50:08.636466', '2025-04-15 19:46:46.672993');

-- ----------------------------
-- Table structure for asm_course_section
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_course_section";
CREATE TABLE "public"."asm_course_section" (
  "id" int4 NOT NULL DEFAULT nextval('asm_course_section_id_seq'::regclass),
  "title" varchar COLLATE "pg_catalog"."default",
  "duration" int4,
  "order" int4,
  "is_free" bool,
  "video_url" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "is_published" bool,
  "course_id" int4
)
;
COMMENT ON COLUMN "public"."asm_course_section"."id" IS '章节ID';
COMMENT ON COLUMN "public"."asm_course_section"."title" IS '章节标题';
COMMENT ON COLUMN "public"."asm_course_section"."duration" IS '时长(秒)';
COMMENT ON COLUMN "public"."asm_course_section"."order" IS '排序';
COMMENT ON COLUMN "public"."asm_course_section"."is_free" IS '是否免费';
COMMENT ON COLUMN "public"."asm_course_section"."video_url" IS '视频URL';
COMMENT ON COLUMN "public"."asm_course_section"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_course_section"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_course_section"."is_published" IS '是否发布';
COMMENT ON COLUMN "public"."asm_course_section"."course_id" IS '关联课程ID';
COMMENT ON TABLE "public"."asm_course_section" IS '课程章节表';

-- ----------------------------
-- Records of asm_course_section
-- ----------------------------
INSERT INTO "public"."asm_course_section" VALUES (47, '第九章：股权激励实施的4大雷区', 1140, 9, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737602040723-09%E3%80%81%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E5%AE%9E%E6%96%BD%E7%9A%844%E5%A4%A7%E9%9B%B7%E5%8C%BA.mp4', '2025-01-23 03:17:53.277', '2025-01-23 03:17:53.277', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (46, '第八章：股权治理', 1140, 8, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737601780766-08%E3%80%81%E8%82%A1%E6%9D%83%E6%B2%BB%E7%90%86.mp4', '2025-01-23 03:13:08.122', '2025-01-23 03:13:08.122', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (45, '第七章：股权激励方案设计', 3240, 7, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737601128091-07%E3%80%81%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E6%96%B9%E6%A1%88%E8%AE%BE%E8%AE%A1.mp4', '2025-01-23 03:09:02.722', '2025-01-23 03:09:02.722', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (44, '第六章：股权模式灵活应用', 1800, 6, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737600687583-06%E3%80%81%E8%82%A1%E6%9D%83%E6%A8%A1%E5%BC%8F%E7%81%B5%E6%B4%BB%E5%BA%94%E7%94%A8.mp4', '2025-01-23 02:56:58.086', '2025-01-23 02:56:58.086', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (43, '第五章：股权激励常用模式', 3900, 5, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737599903454-05%E3%80%81%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E5%B8%B8%E7%94%A8%E6%A8%A1%E5%BC%8F.mp4', '2025-01-23 02:50:09.911', '2025-01-23 02:50:09.911', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (42, '第四章：股权激励原则', 2340, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737599026227-04%E3%80%81%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E5%8E%9F%E5%88%99.mp4', '2025-01-23 02:34:16.341', '2025-01-23 02:34:16.341', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (41, '第三章：股权相关认知', 1020, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737598669926-03%E3%80%81%E8%82%A1%E6%9D%83%E7%9B%B8%E5%85%B3%E8%AE%A4%E7%9F%A5.mp4', '2025-01-23 02:21:18.176', '2025-01-23 02:34:31.145', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (40, '第二章：资本时代为何要股改？', 600, 2, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737598246760-02%E3%80%81%E8%B5%84%E6%9C%AC%E6%97%B6%E4%BB%A3%E4%B8%BA%E4%BD%95%E8%A6%81%E8%82%A1%E6%94%B9.mp4', '2025-01-23 02:15:35.912', '2025-01-23 02:15:51.48', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (39, '第一章：转型时代为何一定要做股改？', 1080, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737597958302-01%E3%80%81%E8%BD%AC%E5%9E%8B%E6%97%B6%E4%BB%A3%E4%B8%BA%E4%BD%95%E4%B8%80%E5%AE%9A%E8%A6%81%E5%81%9A%E8%82%A1%E6%94%B9.mp4', '2025-01-23 02:09:58.161', '2025-01-23 02:09:58.161', 't', 2);
INSERT INTO "public"."asm_course_section" VALUES (38, '第十四章：如何取得卓越的经营业绩？', 1560, 14, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737548172531-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC14%E7%AB%A0%20%E5%A6%82%E4%BD%95%E5%8F%96%E5%BE%97%E5%8D%93%E8%B6%8A%E7%9A%84%E7%BB%8F%E8%90%A5%E4%B8%9A%E7%BB%A9_ev_ev.mp4', '2025-01-22 12:22:46.842', '2025-01-22 12:22:46.842', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (37, '第十三章：重新审视营销', 1320, 13, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737547794678-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC13%E7%AB%A0%20%E9%87%8D%E6%96%B0%E5%AE%A1%E8%A7%86%E8%90%A5%E9%94%80_ev_ev.mp4', '2025-01-22 12:15:02.363', '2025-01-22 12:15:02.363', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (36, '第十二章：做营销一定要花钱吗?', 960, 12, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737547463231-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC12%E7%AB%A0%20%E5%81%9A%E8%90%A5%E9%94%80%E4%B8%80%E5%AE%9A%E8%A6%81%E8%8A%B1%E9%92%B1%E5%90%97%EF%BC%9F_ev_ev.mp4', '2025-01-22 12:08:32.035', '2025-01-22 12:08:32.035', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (35, '第十一章：写出有效的口号', 1080, 11, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737547174781-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC11%E7%AB%A0%20%E5%86%99%E5%87%BA%E6%9C%89%E6%95%88%E7%9A%84%E5%8F%A3%E5%8F%B7_ev_ev.mp4', '2025-01-22 12:03:43.963', '2025-01-22 12:03:43.963', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (34, '第十章：如何设计一家生意兴隆的店铺2.0', 780, 10, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737546956589-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC10%E7%AB%A0%20%E5%A6%82%E4%BD%95%E8%AE%BE%E8%AE%A1%E4%B8%80%E5%AE%B6%E7%94%9F%E6%84%8F%E5%85%B4%E9%9A%86%E7%9A%84%E5%BA%97%E9%93%BA2.0%EF%BC%9F_ev_ev.mp4', '2025-01-22 11:58:55.678', '2025-01-22 12:08:54.177', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (33, '第九章：如何设计一家生意兴隆的店铺1.0', 1080, 9, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737546644907-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC9%E7%AB%A0%20%E5%A6%82%E4%BD%95%E8%AE%BE%E8%AE%A1%E4%B8%80%E5%AE%B6%E7%94%9F%E6%84%8F%E5%85%B4%E9%9A%86%E7%9A%84%E5%BA%97%E9%93%BA1.0%EF%BC%9F_ev_ev.mp4', '2025-01-22 11:55:03.789', '2025-01-22 12:09:05.183', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (32, '第八章：用超级符号降低成本', 1080, 8, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737546279877-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC8%E7%AB%A0%20%E7%94%A8%E8%B6%85%E7%BA%A7%E7%AC%A6%E5%8F%B7%E9%99%8D%E4%BD%8E%E6%88%90%E6%9C%AC_ev_ev.mp4', '2025-01-22 11:48:38.01', '2025-01-22 11:48:38.01', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (31, ' 第七章：定价与精益创业', 540, 7, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737546038286-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC7%E7%AB%A0%20%E5%AE%9A%E4%BB%B7%E4%B8%8E%E7%B2%BE%E7%9B%8A%E5%88%9B%E4%B8%9A_ev_ev.mp4', '2025-01-22 11:43:48.419', '2025-01-22 11:43:48.419', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (30, '第六章：基于顾客视角的几种定价方法 ', 840, 6, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737545812965-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC6%E7%AB%A0%20%E5%9F%BA%E4%BA%8E%E9%A1%BE%E5%AE%A2%E8%A7%86%E8%A7%92%E7%9A%84%E5%87%A0%E7%A7%8D%E5%AE%9A%E4%BB%B7%E6%96%B9%E6%B3%95_ev_ev.mp4', '2025-01-22 11:40:13.638', '2025-01-22 11:40:13.638', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (29, '第五章：包装设计的本质', 1380, 5, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737545443433-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC5%E7%AB%A0%20%E5%8C%85%E8%A3%85%E8%AE%BE%E8%AE%A1%E7%9A%84%E6%9C%AC%E8%B4%A8_ev_ev.mp4', '2025-01-22 11:35:55.615', '2025-01-22 11:35:55.615', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (28, '第四章：JTBD理论，及顾客视角 ', 1140, 4, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737545077536-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC4%E7%AB%A0%20JTBD%E7%90%86%E8%AE%BA%EF%BC%8C%E5%8F%8A%E9%A1%BE%E5%AE%A2%E8%A7%86%E8%A7%92_ev_ev.mp4', '2025-01-22 11:29:36.015', '2025-01-22 11:29:53.08', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (19, '无人直播-第十章', 28, 10, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737457089911-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD10.mp4', '2025-01-21 10:58:37.276', '2025-01-23 03:46:08.988', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (48, '01.AIGC到底是什么？一节课带你了解这一技术革命的核心！', 710, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741596344888-01.AIGC%E5%88%B0%E5%BA%95%E6%98%AF%E4%BB%80%E4%B9%88%EF%BC%9F%E4%B8%80%E8%8A%82%E8%AF%BE%E5%B8%A6%E4%BD%A0%E4%BA%86%E8%A7%A3%E8%BF%99%E4%B8%80%E6%8A%80%E6%9C%AF%E9%9D%A9%E5%91%BD%E7%9A%84%E6%A0%B8%E5%BF%83%EF%BC%81.mp4', '2025-03-10 09:06:01.761', '2025-03-26 18:12:41.798', 't', 64);
INSERT INTO "public"."asm_course_section" VALUES (49, '02.巨人肩膀上的deepseek -认知篇', 700, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741597646121-02.%E5%B7%A8%E4%BA%BA%E8%82%A9%E8%86%80%E4%B8%8A%E7%9A%84deepseek%20-%E8%AE%A4%E7%9F%A5%E7%AF%87.mp4', '2025-03-10 09:17:13.616', '2025-03-26 18:12:45.336', 't', 64);
INSERT INTO "public"."asm_course_section" VALUES (50, '03.巨人肩膀上的deepseek- 技术揭秘', 0, 0, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741598291386-03.%E5%B7%A8%E4%BA%BA%E8%82%A9%E8%86%80%E4%B8%8A%E7%9A%84deepseek-%20%E6%8A%80%E6%9C%AF%E6%8F%AD%E7%A7%98.mp4', '2025-03-10 09:25:21.11', '2025-03-26 18:12:49.086', 't', 64);
INSERT INTO "public"."asm_course_section" VALUES (27, '第三章：名字取得好，公司上市早 ', 1140, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737544727558-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC3%E7%AB%A0%20%E5%90%8D%E5%AD%97%E5%8F%96%E5%BE%97%E5%A5%BD%EF%BC%8C%E5%85%AC%E5%8F%B8%E4%B8%8A%E5%B8%82%E6%97%A9_ev_ev.mp4', '2025-01-22 11:23:04.46', '2025-01-22 11:23:15.104', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (26, '第二章：选个好行业，做个大企业', 1860, 2, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737544248254-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC2%E7%AB%A0%20%E9%80%89%E4%B8%AA%E5%A5%BD%E8%A1%8C%E4%B8%9A%EF%BC%8C%E5%81%9A%E4%B8%AA%E5%A4%A7%E4%BC%81%E4%B8%9A_ev_ev.mp4', '2025-01-22 11:17:49.913', '2025-01-22 11:17:49.913', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (25, '第一章：价值成本原理', 2340, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737543212526-%E5%B0%8F%E9%A9%AC%E5%AE%8B%EF%BC%9A%E8%90%A5%E9%94%80%E5%AE%9E%E6%88%98%E5%8D%87%E7%BA%A7.%E7%AC%AC1%E7%AB%A0%20%E4%BB%B7%E5%80%BC%E6%88%90%E6%9C%AC%E5%8E%9F%E7%90%86_ev_ev.mp4', '2025-01-22 11:05:31', '2025-01-22 11:08:56.243', 't', 5);
INSERT INTO "public"."asm_course_section" VALUES (18, '无人直播-第九章', 34, 9, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737457038287-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD9.mp4', '2025-01-21 10:57:50.093', '2025-01-23 03:45:52.435', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (17, '无人直播-第八章', 36, 8, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456999590-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD8.mp4', '2025-01-21 10:57:01.977', '2025-01-21 11:01:26.054', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (16, '无人直播-第七章', 30, 7, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456948518-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD7.mp4', '2025-01-21 10:56:06.814', '2025-01-23 03:45:36.657', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (15, '无人直播-第六章', 25, 6, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456820341-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD6.mp4', '2025-01-21 10:55:17.003', '2025-01-23 03:45:23.627', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (14, '无人直播-第五章', 20, 5, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456699777-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD5.mp4', '2025-01-21 10:53:17.422', '2025-01-21 11:00:48.294', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (13, '无人直播-第四章', 21, 4, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456649876-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD4.mp4', '2025-01-21 10:51:13.022', '2025-01-23 03:45:07.672', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (12, '无人直播-第三章', 31, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456579766-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD3.mp4', '2025-01-21 10:50:03.428', '2025-01-21 11:00:58.186', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (11, '无人直播-第二章', 21, 2, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456476459-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD2.mp4', '2025-01-21 10:48:16.215', '2025-01-23 03:44:46.563', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (10, '无人直播-第一章', 22, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737456374993-%E6%97%A0%E4%BA%BA%E7%9B%B4%E6%92%AD1.mp4', '2025-01-21 10:47:05.008', '2025-01-23 03:44:28.263', 't', 3);
INSERT INTO "public"."asm_course_section" VALUES (24, '第五讲：从罗辑思维到得到的思维模型', 1020, 5, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737542535996-05%E8%AE%B2_%20%E4%BB%8E%E7%BD%97%E8%BE%91%E6%80%9D%E7%BB%B4%E5%88%B0%E5%BE%97%E5%88%B0%E7%9A%84%E6%80%9D%E7%BB%B4%E6%A8%A1%E5%9E%8B.mp4', '2025-01-22 10:45:43.808', '2025-01-22 10:45:43.808', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (23, '第四讲：太二酸菜鱼和品牌人格塑造', 2580, 4, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737541833447-04%E8%AE%B2_%20%E5%A4%AA%E4%BA%8C%E9%85%B8%E8%8F%9C%E9%B1%BC%E5%92%8C%E5%93%81%E7%89%8C%E4%BA%BA%E6%A0%BC%E5%A1%91%E9%80%A0.mp4', '2025-01-22 10:39:37.08', '2025-01-22 10:41:05.043', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (22, '第三讲：熊猫不走和消费戏剧', 3600, 3, 'f', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737541005358-03%E8%AE%B2_%20%E7%86%8A%E7%8C%AB%E4%B8%8D%E8%B5%B0%E5%92%8C%E6%B6%88%E8%B4%B9%E6%88%8F%E5%89%A7.mp4', '2025-01-22 10:28:49.98', '2025-01-22 10:40:41.689', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (21, '第二讲：从西贝和云海肴看4P', 3240, 2, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737540249075-02%E8%AE%B2_%20%E4%BB%8E%E8%A5%BF%E8%B4%9D%E5%92%8C%E4%BA%91%E6%B5%B7%E8%82%B4%E7%9C%8B4P.mp4', '2025-01-22 10:14:38.459', '2025-01-22 10:40:25.878', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (20, '第一讲：古茗 战略无人区中的生意', 5280, 1, 't', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737538641675-01%E8%AE%B2_%20%E5%8F%A4%E8%8C%97%EF%BC%9A%E6%88%98%E7%95%A5%E6%97%A0%E4%BA%BA%E5%8C%BA%E4%B8%AD%E7%9A%84%E7%94%9F%E6%84%8F.mp4', '2025-01-22 10:00:48.663', '2025-01-22 10:40:12.408', 't', 4);
INSERT INTO "public"."asm_course_section" VALUES (53, '1', 1, 1, 't', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1747143581-4.mp4', '2025-05-13 21:39:50.409668', '2025-05-13 21:39:50.409689', 't', 3);

-- ----------------------------
-- Table structure for asm_investment_show
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_investment_show";
CREATE TABLE "public"."asm_investment_show" (
  "id" int4 NOT NULL DEFAULT nextval('asm_investment_show_id_seq'::regclass),
  "episode" int4,
  "title" varchar COLLATE "pg_catalog"."default",
  "poster_url" varchar COLLATE "pg_catalog"."default",
  "video_url" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "guest_info" json,
  "views_count" int4,
  "is_published" bool,
  "publish_time" timestamp(6),
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_investment_show"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_investment_show"."episode" IS '期号';
COMMENT ON COLUMN "public"."asm_investment_show"."title" IS '标题';
COMMENT ON COLUMN "public"."asm_investment_show"."poster_url" IS '海报图片URL';
COMMENT ON COLUMN "public"."asm_investment_show"."video_url" IS '视频URL';
COMMENT ON COLUMN "public"."asm_investment_show"."description" IS '节目描述';
COMMENT ON COLUMN "public"."asm_investment_show"."guest_info" IS '嘉宾信息';
COMMENT ON COLUMN "public"."asm_investment_show"."views_count" IS '观看次数';
COMMENT ON COLUMN "public"."asm_investment_show"."is_published" IS '是否发布';
COMMENT ON COLUMN "public"."asm_investment_show"."publish_time" IS '发布时间';
COMMENT ON COLUMN "public"."asm_investment_show"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_investment_show"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_investment_show" IS '创投show历史记录表';

-- ----------------------------
-- Records of asm_investment_show
-- ----------------------------
INSERT INTO "public"."asm_investment_show" VALUES (1, 1, 'NewMoney湖畔Show', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737445298375-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250121154128.jpg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737444671516-%E6%B9%96%E7%95%94%E7%A7%80.mp4', '湖畔 SHOW 企业家商业 IP 公开课震撼来袭！汇聚行业大咖导师，包括黑总王博轩、许云峰、Mark 等大咖云集，9月5日于杭州湖畔创研中心开启会员公开课，剖析商业IP生意逻辑、流量真相，以及 NewMoney 新物种系列发布会，次日更有修然控股企业访学，深度交流学习，助你洞悉商业奥秘，开启创业新征程！', '"主讲导师：\nNewMoney撕董会主理人——黑总\n演讲主题：【祛魅—商业IP流量和新钱的真相】\n\n嘉宾导师：\n直男财经创始人、全网粉丝超4000万——许云峰\n演讲主题：【打造IP，提升品牌影响力】\n\n嘉宾导师：\n天使投资人、浙江行早董事长——Mark\n演讲主题：【以终局思考现在，痛点永远是机会】\n\n特别环节：赫畅脱口秀首秀\n主题：【关于创业的假象】"', 1200, 't', '2023-12-01 20:00:00', '2023-11-25 10:00:00', '2025-01-21 07:47:37.469');
INSERT INTO "public"."asm_investment_show" VALUES (2, 2, 'NewMoney创投Show', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737445682980-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250121151634.jpg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737446024979-%E5%88%9B%E6%8A%95%E7%A7%80.mp4', 'NewMoney创投SHOW与知名大咖投资人深度交流，获项目指引与融资机会；链接高净值 NewMoney 会员，打通上下游资源，实现跨行业学习；携手 NewMoney 黑总及大咖，共掘行业机遇，探寻红利与新钱方向。', '"超强导师阵容震撼登场！\nNewMoney 撕董会主理人黑总，带你洞察商业风云；\n\n梅花创投创始合伙人吴世春，以独到眼光挖掘财富密码；\n\n金慧丰投资董事长、天使联合汇主席周丽霞，带来丰富的投资经验与智慧；\n\n坚果创投董事长王展，开启智慧投资之门。"', 1500, 't', '2023-12-15 20:00:00', '2023-12-10 10:00:00', '2025-01-21 08:01:21.88');

-- ----------------------------
-- Table structure for asm_investment_show_application
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_investment_show_application";
CREATE TABLE "public"."asm_investment_show_application" (
  "id" int4 NOT NULL DEFAULT nextval('asm_investment_show_application_id_seq'::regclass),
  "company_name" varchar COLLATE "pg_catalog"."default",
  "industry" varchar COLLATE "pg_catalog"."default",
  "business" varchar COLLATE "pg_catalog"."default",
  "product" varchar COLLATE "pg_catalog"."default",
  "founder" varchar COLLATE "pg_catalog"."default",
  "team" varchar COLLATE "pg_catalog"."default",
  "attachments" json,
  "status" varchar COLLATE "pg_catalog"."default",
  "user_id" int4,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_investment_show_application"."id" IS 'ID';
COMMENT ON COLUMN "public"."asm_investment_show_application"."company_name" IS '公司介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."industry" IS '所处行业';
COMMENT ON COLUMN "public"."asm_investment_show_application"."business" IS '业务介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."product" IS '产品介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."founder" IS '创始人介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."team" IS '团队介绍';
COMMENT ON COLUMN "public"."asm_investment_show_application"."attachments" IS '附件列表 [{name: "文件名", url: "文件链接"}]';
COMMENT ON COLUMN "public"."asm_investment_show_application"."status" IS '状态:PENDING-待审核,APPROVED-已通过,REJECTED-已拒绝';
COMMENT ON COLUMN "public"."asm_investment_show_application"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_investment_show_application"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_investment_show_application"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_investment_show_application" IS '创投show报名表';

-- ----------------------------
-- Records of asm_investment_show_application
-- ----------------------------
INSERT INTO "public"."asm_investment_show_application" VALUES (4, '智慧出行科技有限公司', '新能源汽车', '专注于新能源汽车智能系统开发', '已开发出行业领先的智能驾驶辅助系统,正在研发完全自动驾驶解决方案', '创始人张三,曾任某知名汽车企业技术总监,拥有15年行业经验', '核心团队来自知名车企和互联网公司,平均行业经验8年以上', '[{"name": "商业计划书.pdf", "url": "https://example.com/files/plan1.pdf"}, {"name": "产品介绍.pdf", "url": "https://example.com/files/product1.pdf"}]', 'PENDING', 3, '2024-01-15 14:30:00', '2024-01-15 14:30:00');
INSERT INTO "public"."asm_investment_show_application" VALUES (5, '未来教育科技公司', '教育科技', '利用AI技术提供个性化教育解决方案', 'AI智适应学习平台,已服务超过100所学校', '创始人李四,前某教育集团技术副总裁,斯坦福AI博士', '研发团队90%以上硕士以上学历,其中博士5名', '[{"name": "融资计划.pdf", "url": "https://example.com/files/plan2.pdf"}, {"name": "团队介绍.pdf", "url": "https://example.com/files/team2.pdf"}]', 'APPROVED', 3, '2024-01-16 09:15:00', '2024-01-16 09:15:00');
INSERT INTO "public"."asm_investment_show_application" VALUES (6, '绿色能源科技有限公司', '新能源', '专注于太阳能储能解决方案研发', '已获得多项储能核心技术专利,产品成本较业界降低30%', '创始人王五,清华新能源专业博士,曾创建某知名储能企业', '核心团队均有世界500强相关领域工作经验', '[{"name": "专利文件.pdf", "url": "https://example.com/files/patent3.pdf"}, {"name": "项目介绍.pdf", "url": "https://example.com/files/project3.pdf"}]', 'REJECTED', 3, '2024-01-17 11:20:00', '2024-01-17 11:20:00');
INSERT INTO "public"."asm_investment_show_application" VALUES (7, 'ww', 'e', 'er', 're', 'er', 'er', '[]', 'PENDING', 3, '2025-01-07 14:14:26.156368', '2025-01-07 14:14:26.157369');
INSERT INTO "public"."asm_investment_show_application" VALUES (8, '1', '2', '3', '4', '5', '6', '[]', 'PENDING', 3, '2025-01-07 14:15:51.650892', '2025-01-07 14:15:51.650892');
INSERT INTO "public"."asm_investment_show_application" VALUES (9, '1', '2', '3', '4', '5', '6', '[]', 'PENDING', 3, '2025-01-07 14:16:51.966364', '2025-01-07 14:16:51.966364');
INSERT INTO "public"."asm_investment_show_application" VALUES (10, '1', '2', '3', '4', '5', '6', '[]', 'PENDING', 3, '2025-01-07 14:18:31.907122', '2025-01-07 14:18:31.907122');
INSERT INTO "public"."asm_investment_show_application" VALUES (11, '2', '3', '4', '5', '6', '7', '[{"name": "1736261840-MbCVyO7jZoHp44816374da860b8993316caf4cf46753.pdf", "url": "https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1736261840-MbCVyO7jZoHp44816374da860b8993316caf4cf46753.pdf"}]', 'PENDING', 3, '2025-01-07 14:57:29.286903', '2025-01-07 14:57:29.286903');
INSERT INTO "public"."asm_investment_show_application" VALUES (12, 's', 'fdfg', 'gsf', 'sfdgs', 'gsdfg', 'sdfg', '[{"name": "1736262161-LIj6NFt2S2NPbad2503e0243f6879dd443a9397dccda.pdf", "url": "https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1736262161-LIj6NFt2S2NPbad2503e0243f6879dd443a9397dccda.pdf"}]', 'PENDING', 3, '2025-01-07 15:02:59.012604', '2025-01-07 15:02:59.012604');

-- ----------------------------
-- Table structure for asm_member_orders
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_member_orders";
CREATE TABLE "public"."asm_member_orders" (
  "id" int4 NOT NULL DEFAULT nextval('asm_member_orders_id_seq'::regclass),
  "order_no" varchar COLLATE "pg_catalog"."default",
  "user_id" int4,
  "amount" float8,
  "status" "public"."member_order_status_enum",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "pay_time" timestamp(6),
  "months" int4
)
;
COMMENT ON COLUMN "public"."asm_member_orders"."id" IS '订单ID';
COMMENT ON COLUMN "public"."asm_member_orders"."order_no" IS '订单编号';
COMMENT ON COLUMN "public"."asm_member_orders"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_member_orders"."amount" IS '订单金额';
COMMENT ON COLUMN "public"."asm_member_orders"."status" IS '订单状态';
COMMENT ON COLUMN "public"."asm_member_orders"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_member_orders"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_member_orders"."pay_time" IS '支付时间';
COMMENT ON COLUMN "public"."asm_member_orders"."months" IS '购买月数';
COMMENT ON TABLE "public"."asm_member_orders" IS '会员订单信息表';

-- ----------------------------
-- Records of asm_member_orders
-- ----------------------------
INSERT INTO "public"."asm_member_orders" VALUES (1, 'MEMBER_1737295632_3', 3, 500, 'PENDING', '2025-01-19 14:07:12.056925', '2025-01-19 14:07:12.056925', NULL, NULL);
INSERT INTO "public"."asm_member_orders" VALUES (2, 'MEMBER_1737295641_3', 3, 500, 'PENDING', '2025-01-19 14:07:21.88714', '2025-01-19 14:07:21.88714', NULL, NULL);
INSERT INTO "public"."asm_member_orders" VALUES (3, 'MEMBER_1739453518_18', 18, 500, 'PENDING', '2025-02-13 21:31:58.638209', '2025-02-13 21:31:58.638225', NULL, 12);
INSERT INTO "public"."asm_member_orders" VALUES (4, 'MEMBER_1740299112_10', 10, 500, 'PENDING', '2025-02-23 16:25:12.362769', '2025-02-23 16:25:12.362788', NULL, 12);
INSERT INTO "public"."asm_member_orders" VALUES (5, 'MEMBER_1740299146_10', 10, 500, 'PENDING', '2025-02-23 16:25:46.847767', '2025-02-23 16:25:46.847786', NULL, 12);

-- ----------------------------
-- Table structure for asm_system_parameters
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_system_parameters";
CREATE TABLE "public"."asm_system_parameters" (
  "id" int4 NOT NULL DEFAULT nextval('asm_system_parameters_id_seq'::regclass),
  "key" varchar COLLATE "pg_catalog"."default",
  "value" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_system_parameters"."id" IS '参数ID';
COMMENT ON COLUMN "public"."asm_system_parameters"."key" IS '参数键';
COMMENT ON COLUMN "public"."asm_system_parameters"."value" IS '参数值';
COMMENT ON COLUMN "public"."asm_system_parameters"."description" IS '参数描述';
COMMENT ON COLUMN "public"."asm_system_parameters"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_system_parameters"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_system_parameters" IS '系统参数表';

-- ----------------------------
-- Records of asm_system_parameters
-- ----------------------------
INSERT INTO "public"."asm_system_parameters" VALUES (1, 'home_banner', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1747143538-82afaba9662431673f3117f28ef775d.jpg', '主页 Banner 图片 URL', '2025-01-20 08:14:58.080845', '2025-02-17 13:50:24.679');

-- ----------------------------
-- Table structure for asm_teacher
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_teacher";
CREATE TABLE "public"."asm_teacher" (
  "id" int4 NOT NULL DEFAULT nextval('asm_teacher_id_seq'::regclass),
  "name" varchar COLLATE "pg_catalog"."default",
  "avatar" varchar COLLATE "pg_catalog"."default",
  "hourly_rate" int4,
  "description" varchar COLLATE "pg_catalog"."default",
  "tags" json,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "poster" varchar COLLATE "pg_catalog"."default",
  "assistant_qrcode" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."asm_teacher"."id" IS '导师ID';
COMMENT ON COLUMN "public"."asm_teacher"."name" IS '导师姓名';
COMMENT ON COLUMN "public"."asm_teacher"."avatar" IS '导师头像URL';
COMMENT ON COLUMN "public"."asm_teacher"."hourly_rate" IS '每小时收费(人民币元)';
COMMENT ON COLUMN "public"."asm_teacher"."description" IS '导师描述信息';
COMMENT ON COLUMN "public"."asm_teacher"."tags" IS '标签列表，如：["人气导师", "从业年丰富"]';
COMMENT ON COLUMN "public"."asm_teacher"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_teacher"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_teacher"."poster" IS '导师海报图URL';
COMMENT ON COLUMN "public"."asm_teacher"."assistant_qrcode" IS '助理二维码URL';
COMMENT ON TABLE "public"."asm_teacher" IS '导师信息表';

-- ----------------------------
-- Records of asm_teacher
-- ----------------------------
INSERT INTO "public"."asm_teacher" VALUES (1, '吴世春', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737447120392-1737438243999-397d0dae815db6acc393b5ee79d2144f.jpeg', 50000, '梅花创投创始合伙人，知名天使投资人。曾投资大掌门创造了1500倍回报；投资趣店获得了超过1000倍回报，并且连年荣获清科、投中、《财富》等权威第三方机构评选中国天使投资人10强、中国影响力的30位投资人等荣誉。
', '["投资专家","创业导师","资深导师"]', '2024-12-28 06:59:16.218299', '2025-02-17 02:32:54.32', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737438253338-%E5%90%B4%E4%B8%96%E6%98%A5.jpeg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737438261875-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (5, '小马宋', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737447400386-1737437834462-%E5%B0%8F%E9%A9%AC%E5%AE%8B.jpg', 30000, '独立战略营销顾问，罗辑思维、得到营销外脑，小米生态链企业顾问。曾任暴风魔镜创意合伙人，第九课堂联合创始人，暴风影音高级市场总监，奥美、蓝色光标等。2007年获得戛纳广告节综合传播项铜狮奖，2016年虎嗅网最佳作者。', '["人气导师","营销达人","资深导师","品牌专家"]', '2024-12-28 06:59:16.218299', '2025-02-17 02:32:22.861', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737437846904-CCYH2327_w1207.JPG', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737437598139-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (4, '张培', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737365949591-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120173841.jpg', 8000, '北京钇财咨询服务有限公司创始人', '["资深导师","创业导师"]', '2024-12-28 06:59:16.218299', '2025-02-17 02:32:42.939', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737365975090-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120173841.jpg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737365981135-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (2, '王博轩', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737366117517-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120174138.jpg', 20000, 'NewMoney撕董会主理人、10年媒体人、TOP10财经博主、企业家商业IP领航员', '["资深导师","创业导师"]', '2024-12-28 06:59:16.218299', '2025-02-17 02:33:20.839', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737368583888-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120182232.jpg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737366575170-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (3, '臧其超', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737447343020-t048ecbfcd299e3c1fb.jpg', 20000, '中国咨询式股权投融资专家，深圳三藏资本董事长、著作有《销售团队这样带》《人人都是销售高手》《老板的格局》等。', '["营销达人","资深导师","投资专家"]', '2024-12-28 06:59:16.218299', '2025-02-17 03:02:00.099', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737361524488-40b188342284ad2adc904c7310e6c0e8~tplv-dy-resize-origshort-autoq-75_330.jpeg', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1737355100833-%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250120135921.jpg');
INSERT INTO "public"."asm_teacher" VALUES (19, '张华', 'http://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1741595613992-e7b34dda0fbf15b5e33ff751897e8f1.png', 2000, 'AI 培训导师,薪炬科技·首席培训导师|AIGC提示工程培训导师|企业教练,15年教育培训课程研发和培训经验,工信部教育与考试中心·AIGC提示工程培训导师,中央民族大学特聘程序设计讲师', '[]', '2025-03-10 08:33:56.747', '2025-03-26 18:18:14.48', NULL, NULL);
INSERT INTO "public"."asm_teacher" VALUES (21, '1', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/**********-澜沁国学.png', 100, '1', '[]', '2025-04-15 19:54:33.346578', '2025-04-15 19:54:33.346602', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/**********-澜沁国学.png', 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/**********-ai.jpg');

-- ----------------------------
-- Table structure for asm_user_accounts
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_user_accounts";
CREATE TABLE "public"."asm_user_accounts" (
  "id" int4 NOT NULL DEFAULT nextval('asm_user_accounts_id_seq'::regclass),
  "owner_type" "public"."account_owner_type_enum",
  "user_id" int4,
  "balance" float8,
  "currency" varchar(3) COLLATE "pg_catalog"."default",
  "is_active" bool,
  "created_at" timestamp(6),
  "updated_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_user_accounts"."id" IS '账户ID';
COMMENT ON COLUMN "public"."asm_user_accounts"."owner_type" IS '所有者类型';
COMMENT ON COLUMN "public"."asm_user_accounts"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_user_accounts"."balance" IS '账户余额';
COMMENT ON COLUMN "public"."asm_user_accounts"."currency" IS '货币类型';
COMMENT ON COLUMN "public"."asm_user_accounts"."is_active" IS '是否激活';
COMMENT ON COLUMN "public"."asm_user_accounts"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_user_accounts"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."asm_user_accounts" IS '用户账户表';

-- ----------------------------
-- Records of asm_user_accounts
-- ----------------------------

-- ----------------------------
-- Table structure for asm_user_usages
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_user_usages";
CREATE TABLE "public"."asm_user_usages" (
  "id" int4 NOT NULL DEFAULT nextval('asm_user_usages_id_seq'::regclass),
  "user_id" int4,
  "resource_type" "public"."resource_type_enum",
  "resource_id" varchar COLLATE "pg_catalog"."default",
  "action_type" "public"."action_type_enum",
  "duration" int4,
  "session_id" varchar COLLATE "pg_catalog"."default",
  "tokens_used" int4,
  "total_price" float8,
  "currency" varchar COLLATE "pg_catalog"."default",
  "progress" float8,
  "meta_data" jsonb,
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "ip_address" varchar COLLATE "pg_catalog"."default",
  "device_info" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."asm_user_usages"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."asm_user_usages"."resource_type" IS '资源类型';
COMMENT ON COLUMN "public"."asm_user_usages"."resource_id" IS '资源ID';
COMMENT ON COLUMN "public"."asm_user_usages"."action_type" IS '动作类型';
COMMENT ON COLUMN "public"."asm_user_usages"."duration" IS '使用时长(秒)';
COMMENT ON COLUMN "public"."asm_user_usages"."session_id" IS '会话ID';
COMMENT ON COLUMN "public"."asm_user_usages"."tokens_used" IS '使用的令牌数';
COMMENT ON COLUMN "public"."asm_user_usages"."total_price" IS '总价格';
COMMENT ON COLUMN "public"."asm_user_usages"."currency" IS '货币';
COMMENT ON COLUMN "public"."asm_user_usages"."progress" IS '进度百分比';
COMMENT ON COLUMN "public"."asm_user_usages"."meta_data" IS '元数据';
COMMENT ON COLUMN "public"."asm_user_usages"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_user_usages"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_user_usages"."ip_address" IS 'IP地址';
COMMENT ON COLUMN "public"."asm_user_usages"."device_info" IS '设备信息';
COMMENT ON TABLE "public"."asm_user_usages" IS '用户用量表';

-- ----------------------------
-- Records of asm_user_usages
-- ----------------------------

-- ----------------------------
-- Table structure for asm_users
-- ----------------------------
DROP TABLE IF EXISTS "public"."asm_users";
CREATE TABLE "public"."asm_users" (
  "id" int4 NOT NULL DEFAULT nextval('asm_users_id_seq'::regclass),
  "username" varchar(50) COLLATE "pg_catalog"."default",
  "nickname" varchar(50) COLLATE "pg_catalog"."default",
  "password" varchar(100) COLLATE "pg_catalog"."default",
  "points" int4,
  "user_type" "public"."user_type_enum",
  "phone" varchar(20) COLLATE "pg_catalog"."default",
  "avatar_url" varchar COLLATE "pg_catalog"."default",
  "openid" varchar(50) COLLATE "pg_catalog"."default",
  "created_at" timestamp(6),
  "updated_at" timestamp(6),
  "birthday" date,
  "address" varchar COLLATE "pg_catalog"."default",
  "gender" "public"."gender_enum",
  "membership_expires_at" timestamp(6)
)
;
COMMENT ON COLUMN "public"."asm_users"."username" IS '用户名';
COMMENT ON COLUMN "public"."asm_users"."nickname" IS '用户昵称';
COMMENT ON COLUMN "public"."asm_users"."password" IS '密码(加密存储)';
COMMENT ON COLUMN "public"."asm_users"."points" IS '积分';
COMMENT ON COLUMN "public"."asm_users"."user_type" IS '用户类型';
COMMENT ON COLUMN "public"."asm_users"."phone" IS '手机号';
COMMENT ON COLUMN "public"."asm_users"."avatar_url" IS '头像URL';
COMMENT ON COLUMN "public"."asm_users"."openid" IS '微信OpenID';
COMMENT ON COLUMN "public"."asm_users"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."asm_users"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."asm_users"."birthday" IS '出生日期';
COMMENT ON COLUMN "public"."asm_users"."address" IS '地址';
COMMENT ON COLUMN "public"."asm_users"."gender" IS '性别';
COMMENT ON COLUMN "public"."asm_users"."membership_expires_at" IS '会员到期时间';
COMMENT ON TABLE "public"."asm_users" IS '用户信息表';

-- ----------------------------
-- Records of asm_users
-- ----------------------------
INSERT INTO "public"."asm_users" VALUES (9, '18610256227', '用户6227', NULL, 0, 'WECHAT', '18610256227', 'https://i.pravatar.cc/300', 'o_Qzw6_K68odXcyIwq3c7NA9xtuo', '2025-01-20 01:04:42.161182', '2025-01-20 01:04:42.161201', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (11, '13552474018', '用户4018', NULL, 0, 'WECHAT', '13552474018', 'https://i.pravatar.cc/300', 'o_Qzw6_k-q3QXOpEO6Av5DVt0PDc', '2025-01-21 16:37:55.032074', '2025-01-21 16:37:55.032101', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (12, '13811303843', '用户3843', NULL, 0, 'WECHAT', '13811303843', 'https://i.pravatar.cc/300', 'o_Qzw60DEHjMCpunS_FfLcPeR-ck', '2025-01-21 18:54:12.982779', '2025-01-21 18:54:12.982804', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (39, '15988015622', '用户5622', NULL, 0, 'WECHAT', '15988015622', 'https://i.pravatar.cc/300', 'o_Qzw67tpL7PyEoFWfSnGmeDnChg', '2025-02-21 11:26:15.413304', '2025-02-21 11:26:15.413326', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (52, '18218076713', '用户6713', NULL, 0, 'WECHAT', '18218076713', 'https://i.pravatar.cc/300', 'o_Qzw60KZxH_-p33B10j8rBXNOjk', '2025-02-27 19:50:02.821384', '2025-02-27 19:50:02.821404', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (40, '18758858235', '用户8235', NULL, 0, 'WECHAT', '18758858235', 'https://i.pravatar.cc/300', 'o_Qzw6x758aiy3meBI4_CvVElIsE', '2025-02-21 14:20:54.063922', '2025-02-21 14:20:54.063943', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (41, '15877141345', '用户1345', NULL, 0, 'WECHAT', '15877141345', 'https://i.pravatar.cc/300', 'o_Qzw66CiPCJaX9kCuw22K1JV9Yg', '2025-02-21 15:03:23.503092', '2025-02-21 15:03:23.503115', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (46, '18873372105', '用户2105', NULL, 0, 'WECHAT', '18873372105', 'https://i.pravatar.cc/300', 'o_Qzw6yRIFe4hhfJRmN2ijndukw8', '2025-02-25 09:59:16.452379', '2025-02-25 09:59:16.452402', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (47, '13520816140', '用户6140', NULL, 0, 'WECHAT', '13520816140', 'https://i.pravatar.cc/300', 'o_Qzw6ziT73vNQ5xWz-_iRVDJ-hc', '2025-02-25 14:02:29.057125', '2025-02-25 14:02:29.057146', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (15, '13368691641', '用户1641', NULL, 0, 'WECHAT', '13368691641', 'https://i.pravatar.cc/300', 'o_Qzw657hodMkT91lwdMuc-tmBgM', '2025-02-02 23:45:12.663416', '2025-02-02 23:45:12.66346', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (42, '7901639795', '用户9795', NULL, 0, 'WECHAT', '7901639795', 'https://i.pravatar.cc/300', 'o_Qzw6zEt6tJHTRl_Wc9BEshoc24', '2025-02-21 19:58:28.829162', '2025-02-21 19:58:28.829184', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (48, '13671118396', '用户8396', NULL, 0, 'WECHAT', '13671118396', 'https://i.pravatar.cc/300', 'o_Qzw6wOfZmfK9cvgRY7khm4BVKU', '2025-02-26 10:20:36.166719', '2025-02-26 10:20:36.16674', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (16, '15699999325', '用户9325', NULL, 0, 'WECHAT', '15699999325', 'https://i.pravatar.cc/300', 'o_Qzw6201cVKE-YJz5fixbJew7Xw', '2025-02-13 10:44:36.323074', '2025-02-13 10:44:36.323101', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (17, '15811082418', '用户2418', NULL, 0, 'WECHAT', '15811082418', 'https://i.pravatar.cc/300', 'o_Qzw65f4IRcnhMdgpZbvdjL5KeM', '2025-02-13 20:13:48.205973', '2025-02-13 20:13:48.206', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (18, '15121117928', '用户7928', NULL, 0, 'WECHAT', '15121117928', 'https://i.pravatar.cc/300', 'o_Qzw67WGPhUQrmFaNkwUomcPZLU', '2025-02-13 21:27:11.263591', '2025-02-13 21:27:11.263614', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (49, '15311816560', '用户6560', NULL, 0, 'WECHAT', '15311816560', 'https://i.pravatar.cc/300', 'o_Qzw63X2Y7I_QG1EcJx24g2IGvk', '2025-02-26 11:01:33.571166', '2025-02-26 11:01:33.571199', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (19, '17687007110', '用户7110', NULL, 0, 'WECHAT', '17687007110', 'https://i.pravatar.cc/300', 'o_Qzw689nmwOYrGH4WzwinO7VxS8', '2025-02-17 10:08:35.117843', '2025-02-17 10:08:35.117867', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (20, '13810391813', '用户1813', NULL, 0, 'WECHAT', '13810391813', 'https://i.pravatar.cc/300', 'o_Qzw6xTwwfhsCgJE8m1BTUFsamg', '2025-02-17 11:07:41.138937', '2025-02-17 11:07:41.138959', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (21, '18729016400', '用户6400', NULL, 0, 'WECHAT', '18729016400', 'https://i.pravatar.cc/300', 'o_Qzw65Ncll1YUAfXuqtectqIIj8', '2025-02-17 13:01:11.881731', '2025-02-17 13:01:11.881754', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (22, '17600738083', '用户8083', NULL, 0, 'WECHAT', '17600738083', 'https://i.pravatar.cc/300', 'o_Qzw6_igqiFZUMxI9fRFNHVCABk', '2025-02-17 13:14:14.58012', '2025-02-17 13:14:14.580142', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (23, '18601961211', '用户1211', NULL, 0, 'WECHAT', '18601961211', 'https://i.pravatar.cc/300', 'o_Qzw687V9CN70aWhbBQ5q4K8hUA', '2025-02-17 15:39:46.215582', '2025-02-17 15:39:46.215605', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (24, '13520555545', '用户5545', NULL, 0, 'WECHAT', '13520555545', 'https://i.pravatar.cc/300', 'o_Qzw6yIttYs7KAOafwttjOVH4AQ', '2025-02-17 17:31:00.829759', '2025-02-17 17:31:00.829782', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (25, '15140470502', '用户0502', NULL, 0, 'WECHAT', '15140470502', 'https://i.pravatar.cc/300', 'o_Qzw6_TiqdL1Oy6-YKbs5v0IWEU', '2025-02-18 10:14:47.980092', '2025-02-18 10:14:47.980114', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (43, '18539959791', '用户9791', NULL, 0, 'WECHAT', '18539959791', 'https://i.pravatar.cc/300', 'o_Qzw68-sjtztadCTDOK4xo75zj8', '2025-02-22 15:30:09.311269', '2025-02-22 15:30:09.311292', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (26, '15810795723', '用户5723', NULL, 0, 'WECHAT', '15810795723', 'https://i.pravatar.cc/300', 'o_Qzw62gKIIghCWGNG-AEVHZrj_A', '2025-02-18 16:12:20.314674', '2025-02-18 16:12:20.314696', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (27, '15629052256', '用户2256', NULL, 0, 'WECHAT', '15629052256', 'https://i.pravatar.cc/300', 'o_Qzw61ZOzO5myaHcwni6BvuLhzc', '2025-02-20 14:22:18.538034', '2025-02-20 14:22:18.538056', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (28, '18017161986', '用户1986', NULL, 0, 'WECHAT', '18017161986', 'https://i.pravatar.cc/300', 'o_Qzw64VLBvsXbWaWPI5TpVEtbUc', '2025-02-20 14:49:15.03489', '2025-02-20 14:49:15.034912', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (29, '15529034518', '用户4518', NULL, 0, 'WECHAT', '15529034518', 'https://i.pravatar.cc/300', 'o_Qzw60kYr4m5w3tl7qAS1aIo4Jw', '2025-02-20 15:05:18.084572', '2025-02-20 15:05:18.084592', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (30, '18621638776', '用户8776', NULL, 0, 'WECHAT', '18621638776', 'https://i.pravatar.cc/300', 'o_Qzw6xUX4Uz3H4BC86Wt01VWkqg', '2025-02-20 15:10:35.296272', '2025-02-20 15:10:35.296293', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (31, '18602424999', '用户4999', NULL, 0, 'WECHAT', '18602424999', 'https://i.pravatar.cc/300', 'o_Qzw6wZYYKen2Q95Dm7NFbP9tus', '2025-02-20 15:10:46.073905', '2025-02-20 15:10:46.073926', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (32, '13678195666', '用户5666', NULL, 0, 'WECHAT', '13678195666', 'https://i.pravatar.cc/300', 'o_Qzw6_fS1Ks0gNLpSmujpLKB-os', '2025-02-20 15:11:20.789343', '2025-02-20 15:11:20.789364', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (33, '18111591976', '用户1976', NULL, 0, 'WECHAT', '18111591976', 'https://i.pravatar.cc/300', 'o_Qzw68TgbFMg1o6RFAKpmfYzyqg', '2025-02-20 15:11:24.632146', '2025-02-20 15:11:24.632167', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (34, '15380776922', '用户6922', NULL, 0, 'WECHAT', '15380776922', 'https://i.pravatar.cc/300', 'o_Qzw61_yG2ZEB0uKs87nq7hyYaI', '2025-02-20 15:14:22.343025', '2025-02-20 15:14:22.343049', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (35, '13858100824', '用户0824', NULL, 0, 'WECHAT', '13858100824', 'https://i.pravatar.cc/300', 'o_Qzw63UNVTX8Futwg1YbOwKZw2M', '2025-02-20 15:17:56.156444', '2025-02-20 15:17:56.156468', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (36, '15757181822', '用户1822', NULL, 0, 'WECHAT', '15757181822', 'https://i.pravatar.cc/300', 'o_Qzw61oLoUIiy2KZ8ko4-K23Pl0', '2025-02-20 15:35:36.362427', '2025-02-20 15:35:36.362447', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (50, '13910880907', '用户0907', NULL, 0, 'WECHAT', '13910880907', 'https://i.pravatar.cc/300', 'o_Qzw65OvJEeXGI1pg_eVo5WrERw', '2025-02-26 12:32:48.797545', '2025-02-26 12:32:48.797567', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (53, '18688849331', '用户9331', NULL, 0, 'WECHAT', '18688849331', 'https://i.pravatar.cc/300', 'o_Qzw6yDycP2fJWyg2evsRGtawIQ', '2025-02-28 15:49:02.078539', '2025-02-28 15:49:02.07856', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (51, '13008899973', '用户9973', NULL, 0, 'WECHAT', '13008899973', 'https://i.pravatar.cc/300', 'o_Qzw69ACEHLSShvfthDJt_Ee66U', '2025-02-26 16:29:22.182857', '2025-02-26 16:29:22.182877', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (45, '18601128300', '用户8300', NULL, 0, 'WECHAT', '18601128300', 'https://i.pravatar.cc/300', 'o_Qzw67dO512LDi4DX-3dVIrbM78', '2025-02-24 17:02:40.578455', '2025-02-24 17:02:40.578475', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (56, '15810884808', '用户4808', NULL, 0, 'WECHAT', '15810884808', 'https://i.pravatar.cc/300', 'o_Qzw6wH47ltwZL-Rk5H1b8T9oyA', '2025-03-04 10:35:14.302755', '2025-03-04 10:35:14.302775', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (54, '15041117411', '用户7411', NULL, 0, 'WECHAT', '15041117411', 'https://i.pravatar.cc/300', 'o_Qzw6-86h9nC1bJ5lYPiQ312IkY', '2025-03-01 16:30:22.834029', '2025-03-01 16:30:22.834051', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (55, '15018792228', '用户2228', NULL, 0, 'WECHAT', '15018792228', 'https://i.pravatar.cc/300', 'o_Qzw6yGm3kgBCey-Nf7SjNBy8I0', '2025-03-01 22:31:14.98514', '2025-03-01 22:31:14.985161', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (57, '15202075682', '用户5682', NULL, 0, 'WECHAT', '15202075682', 'https://i.pravatar.cc/300', 'o_Qzw6zWwNjvWxZFR7MRYWYc4SJ8', '2025-03-05 18:54:19.131958', '2025-03-05 18:54:19.13198', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (58, '13392491415', '用户1415', NULL, 0, 'WECHAT', '13392491415', 'https://i.pravatar.cc/300', 'o_Qzw64HRYiXRMq9uI6AZwUMVyYQ', '2025-03-06 15:15:31.971167', '2025-03-06 15:15:31.971189', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (8, '18610270707', '用户0707', NULL, 0, 'WECHAT', '18610270707', 'https://i.pravatar.cc/300', 'oYjtg7B-e2ca9wOgB5Mz-hlEcoos', '2025-01-19 18:58:49.295397', '2025-03-29 11:41:29.905838', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (44, '19375278384', '用户8384', NULL, 0, 'WECHAT', '19375278384', 'https://i.pravatar.cc/300', 'oYjtg7IUXgCN0l1uDE7S9OfyQuL0', '2025-02-22 20:25:41.519385', '2025-03-23 17:33:18.321529', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (14, '17770501538', '用户1538', NULL, 0, 'WECHAT', '17770501538', 'https://i.pravatar.cc/300', 'oYjtg7AFiCVu9dZBAnWejYTkzTB0', '2025-01-26 14:51:57.370395', '2025-03-23 17:33:18.321529', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (10, '15810632627', '用户2627', NULL, 0, 'WECHAT', '15810632627', 'https://i.pravatar.cc/300', 'oYjtg7K7Tv1iedq0EhEBtxlUL5J8', '2025-01-20 02:10:23.536623', '2025-03-23 17:33:18.321529', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (3, '13271976859', '用户6859', NULL, 0, 'WECHAT', '13271976859', 'https://i.pravatar.cc/300', 'oYjtg7A0QXkNBXaLJoNPf_prZqoE', '2024-12-29 10:36:36.974732', '2025-03-23 17:33:18.321529', '2024-12-19', 'dsf', 'MALE', NULL);
INSERT INTO "public"."asm_users" VALUES (13, '13416472917', '用户2917', NULL, 0, 'WECHAT', '13416472917', 'https://i.pravatar.cc/300', 'oYjtg7LkGMZNqGajtoH7U1m350Ys', '2025-01-26 01:19:03.298325', '2025-03-26 11:29:55.538252', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (37, '18610241250', '用户1250', NULL, 0, 'WECHAT', '18610241250', 'https://i.pravatar.cc/300', 'oYjtg7CiWgxAH_1AXoNog-4z47CU', '2025-02-20 16:00:27.780242', '2025-03-29 11:41:30.493957', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (59, '13825025838', '用户5838', NULL, 0, 'WECHAT', '13825025838', 'https://i.pravatar.cc/300', 'o_Qzw6xvSeA39TRkZ8GiiYdhC1F4', '2025-03-06 17:43:51.335311', '2025-03-06 17:43:51.335331', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (61, '18611142922', '用户2922', NULL, 0, 'WECHAT', '18611142922', 'https://i.pravatar.cc/300', 'o_Qzw6zB2eKI9tsrOYj1CxOAVFoY', '2025-03-07 11:41:51.343265', '2025-03-07 11:41:51.343285', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (62, '13077836722', '用户6722', NULL, 0, 'WECHAT', '13077836722', 'https://i.pravatar.cc/300', 'o_Qzw698pO-j1GbTl7jq3qoMOC8s', '2025-03-07 13:14:42.108488', '2025-03-07 13:14:42.108511', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (63, '18764665920', '用户5920', NULL, 0, 'WECHAT', '18764665920', 'https://i.pravatar.cc/300', 'o_Qzw6_lkfpwgzspo2nKsjonouZ4', '2025-03-07 18:09:27.508767', '2025-03-07 18:09:27.508789', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (64, '15167102777', '用户2777', NULL, 0, 'WECHAT', '15167102777', 'https://i.pravatar.cc/300', 'o_Qzw6_2rDtGxeeKsuRZlR155K_c', '2025-03-07 21:42:24.424285', '2025-03-07 21:42:24.424318', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (65, '15063337832', '用户7832', NULL, 0, 'WECHAT', '15063337832', 'https://i.pravatar.cc/300', 'o_Qzw672kU_jSoMo0j7fZ0tF2KEo', '2025-03-08 11:19:06.912772', '2025-03-08 11:19:06.912801', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (66, '18857659458', '用户9458', NULL, 0, 'WECHAT', '18857659458', 'https://i.pravatar.cc/300', 'o_Qzw6zWApp0VczHooQINlLS2OEc', '2025-03-08 13:01:12.654851', '2025-03-08 13:01:12.654873', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (67, '15817913468', '用户3468', NULL, 0, 'WECHAT', '15817913468', 'https://i.pravatar.cc/300', 'o_Qzw62AIkonWOlzhGUYEsaLKEl4', '2025-03-10 12:10:53.970807', '2025-03-10 12:10:53.970836', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (68, '13819475300', '用户5300', NULL, 0, 'WECHAT', '13819475300', 'https://i.pravatar.cc/300', 'o_Qzw63UylWz_fe11JxFMeLNsIxU', '2025-03-10 13:46:45.492037', '2025-03-10 13:46:45.492058', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (69, '18657191367', '用户1367', NULL, 0, 'WECHAT', '18657191367', 'https://i.pravatar.cc/300', 'o_Qzw60bAVW6dfnN_DmA7Q9mUZbs', '2025-03-10 14:08:38.295567', '2025-03-10 14:08:38.295609', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (70, '13005012650', '用户2650', NULL, 0, 'WECHAT', '13005012650', 'https://i.pravatar.cc/300', 'oYjtg7D_m2zGuP4_6Ww6O4CTMoCs', '2025-03-21 21:19:16.646856', '2025-03-21 21:19:16.646881', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (71, '13879571410', '用户1410', NULL, 0, 'WECHAT', '13879571410', 'https://i.pravatar.cc/300', 'oYjtg7IjnoaTVdsbt33xvqmJrcBQ', '2025-03-22 16:19:53.417292', '2025-03-22 16:19:53.417316', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (72, '7562214847', '用户4847', NULL, 0, 'WECHAT', '7562214847', 'https://i.pravatar.cc/300', 'oYjtg7G89sSDElr2G1dHy8I9ysiA', '2025-03-23 05:27:53.014365', '2025-03-23 05:27:53.014405', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (76, '17729916575', '用户6575', NULL, 0, 'LOCAL', '17729916575', 'https://i.pravatar.cc/300', 'oYjtg7HIKWzabdeNVfKWlGyEOvVM', '2025-03-23 17:15:54.061451', '2025-03-23 17:33:18.321529', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (77, '13974018866', '用户8866', NULL, 0, 'WECHAT', '13974018866', 'https://i.pravatar.cc/300', 'oYjtg7H_uyLQVix33McQD59iwNfs', '2025-03-24 13:38:54.535989', '2025-03-24 13:38:54.536026', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (78, '15079933770', '用户3770', NULL, 0, 'WECHAT', '15079933770', 'https://i.pravatar.cc/300', 'oYjtg7Pk00BBmdY7O1yKT7t6uCxk', '2025-03-24 19:33:43.515551', '2025-03-24 19:33:43.515576', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (79, '18801121761', '用户1761', NULL, 0, 'WECHAT', '18801121761', 'https://i.pravatar.cc/300', 'oYjtg7IsHfQlyurFe54XgfGX6N5o', '2025-03-25 13:48:25.618789', '2025-03-25 13:48:25.618815', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (80, '17770576448', '用户6448', NULL, 0, 'WECHAT', '17770576448', 'https://i.pravatar.cc/300', 'oYjtg7P60OxYuwLRwovLJYGCedIQ', '2025-03-25 14:45:40.633878', '2025-03-25 14:45:40.633911', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (60, '13621212038', '用户2038', NULL, 0, 'WECHAT', '13621212038', 'https://i.pravatar.cc/300', 'oYjtg7NBraBvfQJ4jAze7ZMEMyYs', '2025-03-06 22:15:46.53285', '2025-03-26 11:29:55.538252', NULL, NULL, NULL, NULL);
INSERT INTO "public"."asm_users" VALUES (73, '12345678910', '用户8910', NULL, 0, 'LOCAL', '13082523215', 'https://i.pravatar.cc/300', 'oYjtg7HIKWzabdeNVfKWlGyEOvVM', '2025-03-22 21:05:32.453258', '2025-04-15 19:46:46.688821', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_activities_id_seq"
OWNED BY "public"."asm_activities"."id";
SELECT setval('"public"."asm_activities_id_seq"', 2, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_activity_reward_records_id_seq"
OWNED BY "public"."asm_activity_reward_records"."id";
SELECT setval('"public"."asm_activity_reward_records_id_seq"', 57, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_agent_orders_id_seq"
OWNED BY "public"."asm_agent_orders"."id";
SELECT setval('"public"."asm_agent_orders_id_seq"', 2, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_agent_starters_id_seq"
OWNED BY "public"."asm_agent_starters"."id";
SELECT setval('"public"."asm_agent_starters_id_seq"', 38, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_agents_id_seq"
OWNED BY "public"."asm_agents"."id";
SELECT setval('"public"."asm_agents_id_seq"', 64, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_consult_orders_id_seq"
OWNED BY "public"."asm_consult_orders"."id";
SELECT setval('"public"."asm_consult_orders_id_seq"', 48, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_coupons_id_seq"
OWNED BY "public"."asm_coupons"."id";
SELECT setval('"public"."asm_coupons_id_seq"', 68, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_course_id_seq"
OWNED BY "public"."asm_course"."id";
SELECT setval('"public"."asm_course_id_seq"', 75, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_course_orders_id_seq"
OWNED BY "public"."asm_course_orders"."id";
SELECT setval('"public"."asm_course_orders_id_seq"', 79, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_course_section_id_seq"
OWNED BY "public"."asm_course_section"."id";
SELECT setval('"public"."asm_course_section_id_seq"', 53, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_investment_show_application_id_seq"
OWNED BY "public"."asm_investment_show_application"."id";
SELECT setval('"public"."asm_investment_show_application_id_seq"', 12, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_investment_show_id_seq"
OWNED BY "public"."asm_investment_show"."id";
SELECT setval('"public"."asm_investment_show_id_seq"', 6, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_member_orders_id_seq"
OWNED BY "public"."asm_member_orders"."id";
SELECT setval('"public"."asm_member_orders_id_seq"', 5, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_system_parameters_id_seq"
OWNED BY "public"."asm_system_parameters"."id";
SELECT setval('"public"."asm_system_parameters_id_seq"', 5, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_teacher_id_seq"
OWNED BY "public"."asm_teacher"."id";
SELECT setval('"public"."asm_teacher_id_seq"', 21, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_user_accounts_id_seq"
OWNED BY "public"."asm_user_accounts"."id";
SELECT setval('"public"."asm_user_accounts_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_user_usages_id_seq"
OWNED BY "public"."asm_user_usages"."id";
SELECT setval('"public"."asm_user_usages_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."asm_users_id_seq"
OWNED BY "public"."asm_users"."id";
SELECT setval('"public"."asm_users_id_seq"', 80, true);

-- ----------------------------
-- Primary Key structure for table alembic_version
-- ----------------------------
ALTER TABLE "public"."alembic_version" ADD CONSTRAINT "alembic_version_pkc" PRIMARY KEY ("version_num");

-- ----------------------------
-- Indexes structure for table asm_activities
-- ----------------------------
CREATE INDEX "ix_asm_activities_title" ON "public"."asm_activities" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_activities
-- ----------------------------
ALTER TABLE "public"."asm_activities" ADD CONSTRAINT "asm_activities_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_activity_reward_records
-- ----------------------------
CREATE INDEX "ix_asm_activity_reward_records_user_id" ON "public"."asm_activity_reward_records" USING btree (
  "user_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_activity_reward_records
-- ----------------------------
ALTER TABLE "public"."asm_activity_reward_records" ADD CONSTRAINT "asm_activity_reward_records_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table asm_admins
-- ----------------------------
ALTER TABLE "public"."asm_admins" ADD CONSTRAINT "asm_admins_username_key" UNIQUE ("username");

-- ----------------------------
-- Primary Key structure for table asm_admins
-- ----------------------------
ALTER TABLE "public"."asm_admins" ADD CONSTRAINT "asm_users_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_agent_orders
-- ----------------------------
CREATE UNIQUE INDEX "ix_asm_agent_orders_order_no" ON "public"."asm_agent_orders" USING btree (
  "order_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_agent_orders
-- ----------------------------
ALTER TABLE "public"."asm_agent_orders" ADD CONSTRAINT "asm_agent_orders_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_agent_starters
-- ----------------------------
CREATE INDEX "ix_asm_agent_starters_question" ON "public"."asm_agent_starters" USING btree (
  "question" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_agent_starters
-- ----------------------------
ALTER TABLE "public"."asm_agent_starters" ADD CONSTRAINT "asm_agent_starters_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_agents
-- ----------------------------
CREATE INDEX "ix_asm_agents_name" ON "public"."asm_agents" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_agents
-- ----------------------------
ALTER TABLE "public"."asm_agents" ADD CONSTRAINT "asm_agents_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_consult_orders
-- ----------------------------
CREATE UNIQUE INDEX "ix_asm_consult_orders_order_no" ON "public"."asm_consult_orders" USING btree (
  "order_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_consult_orders
-- ----------------------------
ALTER TABLE "public"."asm_consult_orders" ADD CONSTRAINT "asm_consult_orders_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table asm_coupons
-- ----------------------------
ALTER TABLE "public"."asm_coupons" ADD CONSTRAINT "asm_coupons_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_course
-- ----------------------------
CREATE INDEX "ix_asm_course_title" ON "public"."asm_course" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_course
-- ----------------------------
ALTER TABLE "public"."asm_course" ADD CONSTRAINT "asm_course_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_course_orders
-- ----------------------------
CREATE UNIQUE INDEX "ix_asm_course_orders_order_no" ON "public"."asm_course_orders" USING btree (
  "order_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_course_orders
-- ----------------------------
ALTER TABLE "public"."asm_course_orders" ADD CONSTRAINT "asm_course_orders_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_course_section
-- ----------------------------
CREATE INDEX "ix_asm_course_section_title" ON "public"."asm_course_section" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_course_section
-- ----------------------------
ALTER TABLE "public"."asm_course_section" ADD CONSTRAINT "asm_course_section_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_investment_show
-- ----------------------------
CREATE INDEX "ix_asm_investment_show_episode" ON "public"."asm_investment_show" USING btree (
  "episode" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "ix_asm_investment_show_title" ON "public"."asm_investment_show" USING btree (
  "title" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_investment_show
-- ----------------------------
ALTER TABLE "public"."asm_investment_show" ADD CONSTRAINT "asm_investment_show_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_investment_show_application
-- ----------------------------
CREATE INDEX "ix_asm_investment_show_application_company_name" ON "public"."asm_investment_show_application" USING btree (
  "company_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_investment_show_application
-- ----------------------------
ALTER TABLE "public"."asm_investment_show_application" ADD CONSTRAINT "asm_investment_show_application_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_member_orders
-- ----------------------------
CREATE UNIQUE INDEX "ix_asm_member_orders_order_no" ON "public"."asm_member_orders" USING btree (
  "order_no" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_member_orders
-- ----------------------------
ALTER TABLE "public"."asm_member_orders" ADD CONSTRAINT "asm_member_orders_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_system_parameters
-- ----------------------------
CREATE UNIQUE INDEX "ix_asm_system_parameters_key" ON "public"."asm_system_parameters" USING btree (
  "key" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_system_parameters
-- ----------------------------
ALTER TABLE "public"."asm_system_parameters" ADD CONSTRAINT "asm_system_parameters_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_teacher
-- ----------------------------
CREATE INDEX "ix_asm_teacher_name" ON "public"."asm_teacher" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_teacher
-- ----------------------------
ALTER TABLE "public"."asm_teacher" ADD CONSTRAINT "asm_teacher_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table asm_user_accounts
-- ----------------------------
ALTER TABLE "public"."asm_user_accounts" ADD CONSTRAINT "asm_user_accounts_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table asm_user_usages
-- ----------------------------
CREATE INDEX "ix_asm_user_usages_resource_id" ON "public"."asm_user_usages" USING btree (
  "resource_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "ix_asm_user_usages_user_id" ON "public"."asm_user_usages" USING btree (
  "user_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table asm_user_usages
-- ----------------------------
ALTER TABLE "public"."asm_user_usages" ADD CONSTRAINT "asm_user_usages_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table asm_users
-- ----------------------------
ALTER TABLE "public"."asm_users" ADD CONSTRAINT "asm_users_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table asm_agent_orders
-- ----------------------------
ALTER TABLE "public"."asm_agent_orders" ADD CONSTRAINT "asm_agent_orders_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."asm_agents" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."asm_agent_orders" ADD CONSTRAINT "asm_agent_orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_agent_starters
-- ----------------------------
ALTER TABLE "public"."asm_agent_starters" ADD CONSTRAINT "asm_agent_starters_agent_id_fkey" FOREIGN KEY ("agent_id") REFERENCES "public"."asm_agents" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_consult_orders
-- ----------------------------
ALTER TABLE "public"."asm_consult_orders" ADD CONSTRAINT "asm_consult_orders_teacher_id_fkey" FOREIGN KEY ("teacher_id") REFERENCES "public"."asm_teacher" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."asm_consult_orders" ADD CONSTRAINT "asm_consult_orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_coupons
-- ----------------------------
ALTER TABLE "public"."asm_coupons" ADD CONSTRAINT "asm_coupons_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_course
-- ----------------------------
ALTER TABLE "public"."asm_course" ADD CONSTRAINT "asm_course_teacher_id_fkey" FOREIGN KEY ("teacher_id") REFERENCES "public"."asm_teacher" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_course_orders
-- ----------------------------
ALTER TABLE "public"."asm_course_orders" ADD CONSTRAINT "asm_course_orders_course_id_fkey" FOREIGN KEY ("course_id") REFERENCES "public"."asm_course" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."asm_course_orders" ADD CONSTRAINT "asm_course_orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_course_section
-- ----------------------------
ALTER TABLE "public"."asm_course_section" ADD CONSTRAINT "asm_course_section_course_id_fkey" FOREIGN KEY ("course_id") REFERENCES "public"."asm_course" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_investment_show_application
-- ----------------------------
ALTER TABLE "public"."asm_investment_show_application" ADD CONSTRAINT "asm_investment_show_application_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_member_orders
-- ----------------------------
ALTER TABLE "public"."asm_member_orders" ADD CONSTRAINT "asm_member_orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_user_accounts
-- ----------------------------
ALTER TABLE "public"."asm_user_accounts" ADD CONSTRAINT "asm_user_accounts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table asm_user_usages
-- ----------------------------
ALTER TABLE "public"."asm_user_usages" ADD CONSTRAINT "asm_user_usages_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."asm_users" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
